'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Check, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  getCookieConsent,
  saveCookieConsent,
  clearCookieConsent,
  getCookieCategories,
  acceptAllCookies,
  acceptEssentialOnly,
} from '@/lib/cookie-consent';
import { CookieConsent, defaultCookieConsent } from '@/types/cookie-consent';

export function CookiePreferencesManager() {
  const [preferences, setPreferences] = useState<CookieConsent>(defaultCookieConsent);
  const [hasConsented, setHasConsented] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Load existing preferences
    const existingConsent = getCookieConsent();
    if (existingConsent) {
      setPreferences(existingConsent.consent);
      setHasConsented(existingConsent.hasConsented);
    }
  }, []);

  const handlePreferenceChange = (key: keyof CookieConsent, value: boolean) => {
    if (key === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSavePreferences = () => {
    saveCookieConsent(preferences);
    setHasConsented(true);
  };

  const handleAcceptAll = () => {
    acceptAllCookies();
    setPreferences({
      essential: true,
      analytics: true,
      preferences: true,
      marketing: true,
    });
    setHasConsented(true);
  };

  const handleAcceptEssential = () => {
    acceptEssentialOnly();
    setPreferences(defaultCookieConsent);
    setHasConsented(true);
  };

  const handleReset = () => {
    clearCookieConsent();
    setPreferences(defaultCookieConsent);
    setHasConsented(false);
  };

  const cookieCategories = getCookieCategories();

  if (!mounted) {
    return null;
  }

  return (
    <Card className="border-0 shadow-medium bg-card/50 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Settings className="w-6 h-6 text-primary" />
          Gestione Preferenze Cookie
        </CardTitle>
        <CardDescription>
          Gestisci le tue preferenze sui cookie. Puoi modificare queste impostazioni in qualsiasi momento.
          {hasConsented && (
            <Badge variant="secondary" className="ml-2">
              Consenso attivo
            </Badge>
          )}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Cookie Categories */}
        <div className="space-y-4">
          {cookieCategories.map((category) => (
            <Card key={category.key} className="border">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-base flex items-center gap-2">
                      {category.name}
                      {category.required && (
                        <Badge variant="secondary" className="text-xs">
                          Necessari
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {category.description}
                    </CardDescription>
                  </div>
                  <Switch
                    checked={preferences[category.key]}
                    onCheckedChange={(checked) => 
                      handlePreferenceChange(category.key, checked)
                    }
                    disabled={category.required}
                  />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-xs text-muted-foreground">
                  <strong>Esempi:</strong> {category.examples.join(', ')}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          <Button onClick={handleSavePreferences} className="flex-1">
            <Check className="w-4 h-4 mr-2" />
            Salva Preferenze
          </Button>
          <Button variant="outline" onClick={handleAcceptAll} className="flex-1">
            <Cookie className="w-4 h-4 mr-2" />
            Accetta Tutti
          </Button>
          <Button variant="outline" onClick={handleAcceptEssential} className="flex-1">
            Solo Necessari
          </Button>
          <Button variant="outline" onClick={handleReset} className="flex-1">
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>
        
        {/* Status Information */}
        <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
          <p className="font-medium mb-2">Stato attuale:</p>
          <ul className="space-y-1">
            <li>• <strong>Consenso dato:</strong> {hasConsented ? 'Sì' : 'No'}</li>
            <li>• <strong>Cookie tecnici:</strong> Sempre attivi</li>
            <li>• <strong>Cookie analitici:</strong> {preferences.analytics ? 'Attivi' : 'Disattivi'}</li>
            <li>• <strong>Cookie di preferenze:</strong> {preferences.preferences ? 'Attivi' : 'Disattivi'}</li>
            <li>• <strong>Cookie di marketing:</strong> {preferences.marketing ? 'Attivi' : 'Disattivi'}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
