import { NextResponse, NextRequest } from "next/server";
import <PERSON><PERSON><PERSON> from "openai";
import { scabiesOraclePrompt } from "@/lib/scabiesPrompt";
import {
  validateInput,
  getClientIp,
  getSecureHeaders,
  logSecurityEvent,
  apiRateLimiter,
  ValidationSchema
} from "@/lib/security";

// Note: Using custom rate limiter from security module instead of RateLimiterMemory

// Interfaccia per la risposta
interface AIResponse {
  answer: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Input validation schema
const inputSchema: ValidationSchema = {
  input: {
    required: true,
    type: 'object'
  }
};

export async function POST(req: Request) {
  // Generate secure response headers
  const secureHeaders = getSecureHeaders();

  // Log della richiesta in entrata
  const requestId = Math.random().toString(36).substring(2, 9);
  const clientIp = getClientIp(req as unknown as NextRequest);

  console.log(`[${new Date().toISOString()}] [${requestId}] Nuova richiesta da IP: ${clientIp}`);

  // Enhanced rate limiting
  if (!apiRateLimiter.isAllowed(clientIp)) {
    logSecurityEvent('Rate limit exceeded', { clientIp, endpoint: '/api/ask-ai' });
    return NextResponse.json(
      {
        error: "Troppe richieste. Riprova più tardi.",
        retryAfter: Math.ceil((apiRateLimiter.getResetTime(clientIp) - Date.now()) / 1000)
      },
      {
        status: 429,
        headers: {
          ...secureHeaders,
          'Retry-After': '60',
          'X-RateLimit-Remaining': apiRateLimiter.getRemainingRequests(clientIp).toString()
        }
      }
    );
  }

  // Validazione dell'input
  if (!process.env.OPENAI_API_KEY) {
    console.error(`[${requestId}] Errore: OPENAI_API_KEY non configurata`);
    return NextResponse.json(
      { error: "Configurazione del server incompleta" },
      { status: 500, headers: secureHeaders }
    );
  }

  let input;
  try {
    // Parse and validate request body
    const body = await req.json();

    // Enhanced input validation
    const validation = validateInput(body, inputSchema);
    if (!validation.isValid) {
      logSecurityEvent('Invalid input detected', {
        errors: validation.errors,
        clientIp
      });
      return NextResponse.json(
        {
          error: "Input non valido",
          details: validation.errors
        },
        { status: 400, headers: secureHeaders }
      );
    }

    input = validation.sanitizedData.input;

    // Additional security checks
    const inputString = JSON.stringify(input);
    if (inputString.length > 10000) { // Limit input size
      logSecurityEvent('Oversized input detected', {
        size: inputString.length,
        clientIp
      });
      return NextResponse.json(
        { error: "Input troppo grande" },
        { status: 413, headers: secureHeaders }
      );
    }

    console.log(`[${requestId}] Input ricevuto:`, {
      inputLength: inputString.length,
      model: process.env.AI_MODEL || "gpt-4.1-nano"
    });

  } catch (error) {
    logSecurityEvent('Request parsing failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      clientIp
    });
    return NextResponse.json(
      { error: "Formato della richiesta non valido" },
      { status: 400, headers: secureHeaders }
    );
  }

  // Initialize OpenAI client only when needed
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY!,
    timeout: 30000, // 30 secondi di timeout
    maxRetries: 2,
  });
  
  const startTime = Date.now();

  try {
    const model = process.env.AI_MODEL || "gpt-4.1-nano";
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { 
          role: "system", 
          content: scabiesOraclePrompt,
        },
        { 
          role: "user", 
          content: JSON.stringify(input) 
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });

    if (!completion.choices[0]?.message?.content) {
      throw new Error("Risposta vuota dal modello");
    }

    const responseTime = Date.now() - startTime;
    const response: AIResponse = {
      answer: completion.choices[0].message.content,
      model: completion.model,
      usage: completion.usage ? {
        prompt_tokens: completion.usage.prompt_tokens,
        completion_tokens: completion.usage.completion_tokens,
        total_tokens: completion.usage.total_tokens,
      } : undefined,
    };

    console.log(`[${requestId}] Risposta inviata`, {
      responseTime: `${responseTime}ms`,
      model: completion.model,
      usage: completion.usage,
      answerLength: response.answer.length
    });

    return NextResponse.json(response, { headers: secureHeaders });

  } catch (error: unknown) {
    const errorTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = error && typeof error === 'object' && 'code' in error ? (error as { code: string }).code : undefined;
    const errorStatus = error && typeof error === 'object' && 'status' in error ? (error as { status: number }).status : undefined;
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    const errorDetails = {
      requestId,
      error: errorMessage,
      code: errorCode,
      status: errorStatus,
      responseTime: `${errorTime}ms`,
      ...(process.env.NODE_ENV === 'development' && { stack: errorStack }),
    };
    
    console.error(`[${requestId}] Errore chiamata OpenAI:`, errorDetails);

    let status = 500;
    let message = "Errore durante l'elaborazione della richiesta";

    if (errorCode === 'insufficient_quota') {
      status = 402;
      message = "Quota API esaurita";
    } else if (errorCode === 'invalid_api_key') {
      status = 401;
      message = "Chiave API non valida";
    } else if (errorCode === 'context_length_exceeded') {
      status = 400;
      message = "Il contesto della richiesta è troppo lungo";
    } else if (errorMessage.includes('timeout')) {
      status = 504;
      message = "Timeout della richiesta al servizio AI";
    }

    return NextResponse.json(
      { error: message },
      { status, headers: secureHeaders }
    );
  }
}
