/**
 * Form Validation Utilities with Friendly Messages
 * Provides user-friendly validation for the scabies symptom website
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean;
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface FieldValidation {
  [fieldName: string]: ValidationRule[];
}

/**
 * Friendly validation messages for common scenarios
 */
const VALIDATION_MESSAGES = {
  required: "Questo campo è obbligatorio per continuare",
  email: "Inserisci un indirizzo email valido (es. <EMAIL>)",
  phone: "Inserisci un numero di telefono valido (es. +39 ************)",
  minLength: (min: number) => `Inserisci almeno ${min} caratteri`,
  maxLength: (max: number) => `Massimo ${max} caratteri consentiti`,
  password: "La password deve contenere almeno 8 caratteri, una maiuscola e un numero",
  confirmPassword: "Le password non corrispondono",
  age: "L'età deve essere compresa tra 0 e 120 anni",
  symptoms: "Seleziona almeno un sintomo per continuare l'analisi",
  paymentCard: "Inserisci un numero di carta valido",
  paymentExpiry: "Inserisci una data di scadenza valida (MM/AA)",
  paymentCvv: "Inserisci il codice CVV (3 cifre sul retro della carta)",
  terms: "Devi accettare i termini e condizioni per continuare",
  privacy: "Devi accettare l'informativa sulla privacy per continuare",
  name: "Inserisci un nome valido (solo lettere e spazi)",
  surname: "Inserisci un cognome valido (solo lettere e spazi)",
  fiscalCode: "Inserisci un codice fiscale valido",
  postalCode: "Inserisci un CAP valido (5 cifre)",
  city: "Inserisci una città valida",
  address: "Inserisci un indirizzo valido"
};

/**
 * Common validation patterns
 */
const PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^(\+39|0039|39)?[\s]?([0-9]{2,3}[\s]?[0-9]{6,7}|[0-9]{3}[\s]?[0-9]{7})$/,
  name: /^[a-zA-ZÀ-ÿ\s'.-]+$/,
  fiscalCode: /^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/i,
  postalCode: /^[0-9]{5}$/,
  paymentCard: /^[0-9]{13,19}$/,
  paymentExpiry: /^(0[1-9]|1[0-2])\/([0-9]{2})$/,
  paymentCvv: /^[0-9]{3,4}$/
};

/**
 * Validates a single field value against rules
 */
export function validateField(value: any, rules: ValidationRule[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  for (const rule of rules) {
    // Required validation
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      errors.push(rule.message || VALIDATION_MESSAGES.required);
      continue; // Skip other validations if required field is empty
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      continue;
    }

    // String length validations
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push(rule.message || VALIDATION_MESSAGES.minLength(rule.minLength));
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push(rule.message || VALIDATION_MESSAGES.maxLength(rule.maxLength));
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      errors.push(rule.message || "Formato non valido");
    }

    // Custom validation
    if (rule.custom && !rule.custom(value)) {
      errors.push(rule.message || "Valore non valido");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates multiple fields
 */
export function validateForm(data: Record<string, any>, schema: FieldValidation): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {};

  for (const [fieldName, rules] of Object.entries(schema)) {
    results[fieldName] = validateField(data[fieldName], rules);
  }

  return results;
}

/**
 * Checks if entire form is valid
 */
export function isFormValid(validationResults: Record<string, ValidationResult>): boolean {
  return Object.values(validationResults).every(result => result.isValid);
}

/**
 * Gets all errors from form validation
 */
export function getFormErrors(validationResults: Record<string, ValidationResult>): string[] {
  return Object.values(validationResults)
    .flatMap(result => result.errors)
    .filter(Boolean);
}

/**
 * Predefined validation schemas for common forms
 */
export const VALIDATION_SCHEMAS = {
  // User registration form
  registration: {
    name: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.name, message: VALIDATION_MESSAGES.name },
      { minLength: 2, maxLength: 50 }
    ],
    surname: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.name, message: VALIDATION_MESSAGES.surname },
      { minLength: 2, maxLength: 50 }
    ],
    email: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.email, message: VALIDATION_MESSAGES.email }
    ],
    password: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { minLength: 8, message: VALIDATION_MESSAGES.password },
      { 
        custom: (value: string) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/.test(value),
        message: VALIDATION_MESSAGES.password
      }
    ],
    terms: [
      { 
        custom: (value: boolean) => value === true,
        message: VALIDATION_MESSAGES.terms
      }
    ],
    privacy: [
      { 
        custom: (value: boolean) => value === true,
        message: VALIDATION_MESSAGES.privacy
      }
    ]
  },

  // Contact form
  contact: {
    name: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.name, message: VALIDATION_MESSAGES.name },
      { minLength: 2, maxLength: 100 }
    ],
    email: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.email, message: VALIDATION_MESSAGES.email }
    ],
    phone: [
      { pattern: PATTERNS.phone, message: VALIDATION_MESSAGES.phone }
    ],
    message: [
      { required: true, message: "Scrivi il tuo messaggio" },
      { minLength: 10, message: "Il messaggio deve essere di almeno 10 caratteri" },
      { maxLength: 1000, message: "Il messaggio non può superare i 1000 caratteri" }
    ]
  },

  // Payment form
  payment: {
    cardNumber: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.paymentCard, message: VALIDATION_MESSAGES.paymentCard }
    ],
    expiryDate: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.paymentExpiry, message: VALIDATION_MESSAGES.paymentExpiry },
      {
        custom: (value: string) => {
          if (!PATTERNS.paymentExpiry.test(value)) return false;
          const [month, year] = value.split('/');
          const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
          return expiry > new Date();
        },
        message: "La carta è scaduta"
      }
    ],
    cvv: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.paymentCvv, message: VALIDATION_MESSAGES.paymentCvv }
    ],
    cardholderName: [
      { required: true, message: VALIDATION_MESSAGES.required },
      { pattern: PATTERNS.name, message: "Inserisci il nome come appare sulla carta" },
      { minLength: 2, maxLength: 100 }
    ]
  },

  // Scabies test form (for any additional data collection)
  scabiesTest: {
    age: [
      { required: true, message: "L'età è necessaria per una valutazione accurata" },
      {
        custom: (value: number) => value >= 0 && value <= 120,
        message: VALIDATION_MESSAGES.age
      }
    ],
    symptoms: [
      {
        custom: (value: string[]) => Array.isArray(value) && value.length > 0,
        message: VALIDATION_MESSAGES.symptoms
      }
    ]
  }
};

/**
 * Real-time validation helper for form inputs
 */
export function createFieldValidator(rules: ValidationRule[]) {
  return (value: any) => validateField(value, rules);
}

/**
 * Debounced validation for better UX
 */
export function debounceValidation(
  validator: (value: any) => ValidationResult,
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout;
  
  return (value: any, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      const result = validator(value);
      callback(result);
    }, delay);
  };
}
