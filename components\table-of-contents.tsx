"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { List, ChevronRight, Eye } from "lucide-react";

interface Heading {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  headings: Heading[];
  className?: string;
}

export default function TableOfContents({ headings, className = "" }: TableOfContentsProps) {
  const [activeHeading, setActiveHeading] = useState<string>("");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show TOC only if there are enough headings
    setIsVisible(headings.length >= 3);

    if (headings.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveHeading(entry.target.id);
          }
        });
      },
      {
        rootMargin: "-20% 0% -35% 0%",
        threshold: 0
      }
    );

    // Observe all headings
    headings.forEach(({ id }) => {
      const element = document.getElementById(id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [headings]);

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offset = 100; // Account for fixed header
      const elementPosition = element.offsetTop - offset;
      
      window.scrollTo({
        top: elementPosition,
        behavior: "smooth"
      });
    }
  };

  if (!isVisible) return null;

  return (
    <Card className={`sticky top-20 z-sidebar-primary border-0 bg-card/80 backdrop-blur-md shadow-large ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-secondary-accent to-primary flex items-center justify-center">
            <List className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">Indice</h3>
            <p className="text-xs text-muted-foreground">Naviga nell'articolo</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <nav className="space-y-1">
          {headings.map((heading) => {
            const isActive = activeHeading === heading.id;
            const paddingLeft = (heading.level - 1) * 12;
            
            return (
              <Button
                key={heading.id}
                variant="ghost"
                size="sm"
                className={`
                  w-full justify-start text-left h-auto py-2 px-3 transition-all duration-200
                  ${isActive 
                    ? 'bg-primary/10 text-primary border-l-2 border-primary font-medium' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                  }
                `}
                style={{ paddingLeft: `${paddingLeft + 12}px` }}
                onClick={() => scrollToHeading(heading.id)}
              >
                <div className="flex items-center gap-2 w-full">
                  {isActive && <Eye className="w-3 h-3 flex-shrink-0" />}
                  <span className="text-sm leading-relaxed line-clamp-2 text-left">
                    {heading.text}
                  </span>
                  <ChevronRight className="w-3 h-3 flex-shrink-0 ml-auto opacity-50" />
                </div>
              </Button>
            );
          })}
        </nav>
        
        <div className="mt-4 pt-4 border-t border-border/50">
          <Badge variant="outline" className="text-xs">
            <Eye className="w-3 h-3 mr-1" />
            {headings.length} sezioni
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
