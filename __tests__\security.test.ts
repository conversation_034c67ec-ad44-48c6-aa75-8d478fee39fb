import { 
  validateInput, 
  sanitizeHtml, 
  validatePasswordStrength,
  getClientIp,
  generateCsrfToken,
  verifyCsrfToken,
  apiRateLimiter,
  authRateLimiter,
  strictRateLimiter
} from '@/lib/security';
import { NextRequest } from 'next/server';

describe('Security Functions', () => {
  describe('sanitizeHtml', () => {
    it('should remove script tags', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = sanitizeHtml(input);
      expect(result).toBe('scriptalert("xss")/scriptHello');
    });

    it('should remove javascript: protocol', () => {
      const input = 'javascript:alert("xss")';
      const result = sanitizeHtml(input);
      expect(result).toBe('alert("xss")');
    });

    it('should remove event handlers', () => {
      const input = 'onclick="alert(1)" onload="evil()"';
      const result = sanitizeHtml(input);
      expect(result).toBe('');
    });

    it('should handle empty input', () => {
      expect(sanitizeHtml('')).toBe('');
      expect(sanitizeHtml(null as any)).toBe('');
      expect(sanitizeHtml(undefined as any)).toBe('');
    });
  });

  describe('validateInput', () => {
    const schema = {
      name: {
        required: true,
        type: 'string' as const,
        minLength: 2,
        maxLength: 50
      },
      email: {
        required: true,
        type: 'string' as const,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      },
      age: {
        required: false,
        type: 'number' as const
      }
    };

    it('should validate correct input', () => {
      const input = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25
      };
      const result = validateInput(input, schema);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject missing required fields', () => {
      const input = {
        age: 25
      };
      const result = validateInput(input, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'name' is required");
      expect(result.errors).toContain("Field 'email' is required");
    });

    it('should validate string length', () => {
      const input = {
        name: 'A',
        email: '<EMAIL>'
      };
      const result = validateInput(input, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'name' must be at least 2 characters");
    });

    it('should validate email pattern', () => {
      const input = {
        name: 'John Doe',
        email: 'invalid-email'
      };
      const result = validateInput(input, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'email' format is invalid");
    });

    it('should sanitize string inputs', () => {
      const input = {
        name: '<script>alert("xss")</script>John',
        email: '<EMAIL>'
      };
      const result = validateInput(input, schema);
      expect(result.sanitizedData.name).toBe('scriptalert("xss")/scriptJohn');
    });
  });

  describe('validatePasswordStrength', () => {
    it('should accept strong password', () => {
      const result = validatePasswordStrength('StrongP@ssw0rd!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak passwords', () => {
      const result = validatePasswordStrength('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should require minimum length', () => {
      const result = validatePasswordStrength('Sh0rt!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should require uppercase letter', () => {
      const result = validatePasswordStrength('lowercase123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should require lowercase letter', () => {
      const result = validatePasswordStrength('UPPERCASE123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should require number', () => {
      const result = validatePasswordStrength('NoNumbers!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should require special character', () => {
      const result = validatePasswordStrength('NoSpecial123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character');
    });
  });

  describe('CSRF Token Functions', () => {
    it('should generate valid CSRF token', () => {
      const token = generateCsrfToken();
      expect(token).toBeDefined();
      expect(token.length).toBe(64); // 32 bytes in hex = 64 characters
      expect(/^[a-f0-9]+$/.test(token)).toBe(true);
    });

    it('should verify matching CSRF tokens', () => {
      const token = generateCsrfToken();
      const result = verifyCsrfToken(token, token);
      expect(result).toBe(true);
    });

    it('should reject non-matching CSRF tokens', () => {
      const token1 = generateCsrfToken();
      const token2 = generateCsrfToken();
      const result = verifyCsrfToken(token1, token2);
      expect(result).toBe(false);
    });

    it('should reject empty tokens', () => {
      expect(verifyCsrfToken('', '')).toBe(false);
      expect(verifyCsrfToken('token', '')).toBe(false);
      expect(verifyCsrfToken('', 'token')).toBe(false);
    });
  });

  describe('Rate Limiters', () => {
    beforeEach(() => {
      // Clear rate limiter state before each test
      (apiRateLimiter as any).requests.clear();
      (authRateLimiter as any).requests.clear();
      (strictRateLimiter as any).requests.clear();
    });

    it('should allow requests within limit', () => {
      const identifier = 'test-ip';
      expect(apiRateLimiter.isAllowed(identifier)).toBe(true);
      expect(apiRateLimiter.isAllowed(identifier)).toBe(true);
    });

    it('should track remaining requests', () => {
      const identifier = 'test-ip';
      const initial = apiRateLimiter.getRemainingRequests(identifier);
      apiRateLimiter.isAllowed(identifier);
      const after = apiRateLimiter.getRemainingRequests(identifier);
      expect(after).toBe(initial - 1);
    });

    it('should have different limits for different rate limiters', () => {
      const identifier = 'test-ip';
      
      // API rate limiter allows more requests
      for (let i = 0; i < 5; i++) {
        expect(apiRateLimiter.isAllowed(identifier)).toBe(true);
      }
      
      // Strict rate limiter has lower limit
      const strictIdentifier = 'strict-test-ip';
      for (let i = 0; i < 3; i++) {
        expect(strictRateLimiter.isAllowed(strictIdentifier)).toBe(true);
      }
      expect(strictRateLimiter.isAllowed(strictIdentifier)).toBe(false);
    });
  });

  describe('getClientIp', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'x-forwarded-for') return '***********, ********';
            return null;
          })
        },
        ip: undefined
      } as unknown as NextRequest;

      const ip = getClientIp(mockRequest);
      expect(ip).toBe('***********');
    });

    it('should extract IP from x-real-ip header', () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'x-real-ip') return '***********';
            return null;
          })
        },
        ip: undefined
      } as unknown as NextRequest;

      const ip = getClientIp(mockRequest);
      expect(ip).toBe('***********');
    });

    it('should extract IP from cf-connecting-ip header', () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'cf-connecting-ip') return '***********';
            return null;
          })
        },
        ip: undefined
      } as unknown as NextRequest;

      const ip = getClientIp(mockRequest);
      expect(ip).toBe('***********');
    });

    it('should fallback to request.ip', () => {
      const mockRequest = {
        headers: {
          get: jest.fn(() => null)
        },
        ip: '***********'
      } as unknown as NextRequest;

      const ip = getClientIp(mockRequest);
      expect(ip).toBe('***********');
    });

    it('should return unknown if no IP found', () => {
      const mockRequest = {
        headers: {
          get: jest.fn(() => null)
        },
        ip: undefined
      } as unknown as NextRequest;

      const ip = getClientIp(mockRequest);
      expect(ip).toBe('unknown');
    });
  });
});
