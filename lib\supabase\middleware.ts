import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";
import { hasEnvVars } from "../utils";
import { getClientIp, getSecureHeaders, logSecurityEvent } from "@/lib/security";

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  // Add security headers to all responses
  const secureHeaders = getSecureHeaders();
  Object.entries(secureHeaders).forEach(([key, value]) => {
    supabaseResponse.headers.set(key, value);
  });

  // Security logging for suspicious requests
  const clientIp = getClientIp(request);
  const userAgent = request.headers.get('user-agent') || '';
  const path = request.nextUrl.pathname;

  // Log suspicious patterns
  if (
    path.includes('..') ||
    path.includes('<script') ||
    path.includes('javascript:') ||
    userAgent.toLowerCase().includes('bot') && !userAgent.toLowerCase().includes('googlebot')
  ) {
    logSecurityEvent('Suspicious request detected', {
      path,
      userAgent,
      ip: clientIp
    }, request);
  }

  // If the env vars are not set, skip middleware check. You can remove this once you setup the project.
  if (!hasEnvVars) {
    return supabaseResponse;
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value),
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/login",
    "/scabbia-checker",
    "/auth",
    "/blog",
    "/cos-e-la-scabbia",
    "/sintomi",
    "/cause",
    "/diagnosi",
    "/cura",
    "/prevenzione",
    "/contatti",
    "/test-result",
    "/privacy",
    "/termini",
    "/terms",
    "/cookie",
    "/faq",
    "/not-found",
    "/api/create-checkout-session",
    "/api/ask-ai",
    "/api/analyze-answers",
    "/api/webhook"
  ];

  // Check if the current path should be publicly accessible
  const isPublicRoute = publicRoutes.some(route =>
    request.nextUrl.pathname === route ||
    request.nextUrl.pathname.startsWith(route + "/")
  );

  if (
    request.nextUrl.pathname !== "/" &&
    !user &&
    !isPublicRoute
  ) {
    // For API routes, return 401 instead of redirecting
    if (request.nextUrl.pathname.startsWith("/api/")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For regular pages, redirect to login
    const url = request.nextUrl.clone();
    url.pathname = "/auth/login";
    return NextResponse.redirect(url);
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse;
}
