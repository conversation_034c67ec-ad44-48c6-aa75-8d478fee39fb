import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Shield, Home, Users, AlertTriangle, CheckCircle, Info, Droplets, Wind } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Prevenzione della Scabbia | Come Evitare il Contagio - ScabbiaSintomi.it',
  description: 'Sc<PERSON><PERSON> come prevenire la diffusione della scabbia, le misure igieniche da adottare e come proteggere te stesso e la tua famiglia. Guida completa alla prevenzione.',
  keywords: 'prevenzione scabbia, igiene, contagio, misure preventive, protezione famiglia',
};

const preventionMeasures = [
  {
    title: 'Igiene personale',
    icon: Droplets,
    color: 'from-blue-500 to-blue-600',
    measures: [
      'Lavati spesso le mani con acqua e sapone',
      'Fai la doccia quotidianamente',
      'Cambia quotidianamente la biancheria intima e i vestiti',
      'Non condividere oggetti personali come asciugamani o spazzole'
    ]
  },
  {
    title: 'Ambiente domestico',
    icon: Home,
    color: 'from-green-500 to-green-600',
    measures: [
      'Lava regolarmente lenzuola e federe a 60°C',
      'Aspira spesso tappeti, divani e materassi',
      'Mantieni la casa ben areata',
      'Disinfetta le superfici toccate frequentemente'
    ]
  }
];

const contactProtocol = [
  {
    step: 1,
    title: 'Trattamento preventivo',
    description: 'Consulta un medico per valutare la necessità di un trattamento preventivo',
    note: 'La diagnosi precoce e il trattamento tempestivo sono fondamentali per prevenire la diffusione della scabbia.'
  },
  {
    step: 2,
    title: 'Monitora i sintomi',
    description: 'Fai attenzione alla comparsa di prurito o eruzioni cutanee nelle 2-6 settimane successive'
  },
  {
    step: 3,
    title: 'Trattamento simultaneo',
    description: 'Se convivi con qualcuno che ha la scabbia, sottoponiti al trattamento contemporaneamente'
  }
];

export default function Prevenzione() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Prevenzione e Protezione
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Prevenzione</span> della Scabbia
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Scopri le misure più efficaci per proteggere te stesso e la tua famiglia
                dal contagio della scabbia.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="border-0 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-8">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    La prevenzione della scabbia è fondamentale per evitare il contagio e la diffusione dell&apos;infestazione.
                    Ecco le misure più efficaci da adottare per proteggere te stesso e chi ti sta intorno.
                  </p>
                </CardContent>
              </Card>

              {/* Prevention Measures */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Misure di prevenzione
                  </h2>
                  <p className="text-muted-foreground">
                    Le strategie più efficaci per proteggere te stesso e la tua famiglia
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {preventionMeasures.map((category, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-4 mb-4">
                          <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${category.color} flex items-center justify-center shadow-medium`}>
                            <category.icon className="w-6 h-6 text-white" />
                          </div>
                          <CardTitle className="text-xl text-foreground">
                            {category.title}
                          </CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          {category.measures.map((measure, idx) => (
                            <div key={idx} className="flex items-start gap-3 p-3 bg-background/50 rounded-lg">
                              <CheckCircle className="w-5 h-5 text-success flex-shrink-0 mt-0.5" />
                              <span className="text-foreground text-sm">{measure}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Contact Protocol */}
                <Card className="border-0 bg-warning-light/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                      <AlertTriangle className="w-6 h-6 text-warning" />
                      In caso di contatto con persona infetta
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {contactProtocol.map((step, index) => (
                        <div key={index} className="flex items-start gap-4 p-4 bg-background/80 rounded-lg">
                          <div className="w-8 h-8 rounded-full bg-warning flex items-center justify-center flex-shrink-0">
                            <span className="text-sm font-bold text-white">{step.step}</span>
                          </div>
                          <div className="space-y-2">
                            <h4 className="font-semibold text-foreground">{step.title}</h4>
                            <p className="text-muted-foreground text-sm">{step.description}</p>
                            {step.note && (
                              <div className="p-3 bg-primary-light/50 rounded-lg">
                                <p className="text-xs text-primary font-medium">{step.note}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Risk Situations */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Situazioni a rischio
                  </h2>
                  <p className="text-muted-foreground">
                    Ambienti e situazioni che richiedono maggiore attenzione preventiva
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      title: 'Vita comunitaria',
                      description: 'Case di cura, dormitori, carceri',
                      icon: Home,
                      tip: 'Mantieni un\'igiene rigorosa e segnala eventuali casi sospetti'
                    },
                    {
                      title: 'Attività sportive',
                      description: 'Lotta, rugby, judo',
                      icon: Users,
                      tip: 'Fai la doccia subito dopo l\'attività e lava l\'abbigliamento sportivo'
                    },
                    {
                      title: 'Viaggi',
                      description: 'Alloggi comuni, ostelli',
                      icon: Wind,
                      tip: 'Controlla le condizioni igieniche e usa il tuo asciugamano personale'
                    },
                    {
                      title: 'Bambini',
                      description: 'Asili, scuole',
                      icon: Users,
                      tip: 'Insegna ai bambini a non condividere cappelli, sciarpe o spazzole'
                    },
                    {
                      title: 'Assistenza sanitaria',
                      description: 'Ospedali, case di riposo',
                      icon: Shield,
                      tip: 'Usa i DPI e segui i protocolli igienici'
                    },
                    {
                      title: 'Famiglia',
                      description: 'Contatti domestici',
                      icon: Home,
                      tip: 'Tratta tutti i membri della famiglia contemporaneamente in caso di infestazione'
                    },
                  ].map((item, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardContent className="p-6 space-y-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-warning to-warning-light flex items-center justify-center shadow-medium">
                          <item.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-foreground">{item.title}</h3>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                        <div className="p-3 bg-warning-light/20 rounded-lg">
                          <p className="text-xs text-warning font-medium flex items-start gap-2">
                            <Info className="w-3 h-3 flex-shrink-0 mt-0.5" />
                            {item.tip}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Myths Section */}
              <Card className="border-0 bg-secondary/20 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <CheckCircle className="w-6 h-6 text-secondary-accent" />
                    Miti da sfatare sulla prevenzione
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      {
                        myth: "La scabbia colpisce solo le persone con scarsa igiene",
                        fact: "Falso. Chiunque può prendere la scabbia, indipendentemente dal proprio livello di igiene personale."
                      },
                      {
                        myth: "Gli animali domestici possono trasmettere la scabbia umana",
                        fact: "Falso. Gli animali hanno i loro tipi di acari che non sopravvivono a lungo sulla pelle umana."
                      },
                      {
                        myth: "La candeggina uccide tutti gli acari",
                        fact: "La candeggina può irritare la pelle ma non è un trattamento efficace contro la scabbia."
                      },
                      {
                        myth: "Basta lavarsi spesso per prevenire la scabbia",
                        fact: "Il lavaggio frequente non previene la scabbia e può peggiorare l'irritazione della pelle."
                      }
                    ].map((item, index) => (
                      <Card key={index} className="border-0 bg-background/80">
                        <CardContent className="p-4 space-y-2">
                          <p className="font-semibold text-destructive flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4" />
                            {item.myth}
                          </p>
                          <p className="text-foreground flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            {item.fact}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Emergency Protocol */}
              <Card className="border-l-4 border-l-primary bg-primary-light/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Info className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        Cosa fare in caso di sospetto contagio
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Se sospetti di essere entrato in contatto con la scabbia o noti sintomi sospetti:
                      </p>
                      <div className="space-y-3">
                        {[
                          'Rivolgiti al tuo medico di famiglia o a un dermatologo',
                          'Evita contatti stretti con altre persone fino alla diagnosi',
                          'Non iniziare trattamenti fai-da-te senza consulto medico',
                          'Avvisa le persone con cui sei stato a stretto contatto'
                        ].map((step, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-background/80 rounded-lg">
                            <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                              <span className="text-xs font-medium text-primary">{index + 1}</span>
                            </div>
                            <span className="text-foreground text-sm">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Hai dubbi sui sintomi?
                </h3>
                <p className="text-muted-foreground">
                  Fai il nostro test AI per una valutazione immediata e consigli personalizzati.
                </p>
                <div className="bg-gradient-to-r from-success/10 to-primary/10 rounded-lg p-4 mb-6 border border-success/20">
                  <div className="flex items-center justify-center space-x-2 text-sm">
                    <span className="font-semibold text-success">Investimento intelligente: €1</span>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-muted-foreground">Risparmia €99+ rispetto a visita specialistica</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/scabbia-checker">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Test AI €1
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/sintomi">
                    <Button variant="outline" size="lg">
                      Riconosci i Sintomi
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
