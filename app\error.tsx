"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  RefreshCw, 
  Home, 
  AlertTriangle, 
  Stethoscope,
  Heart,
  ArrowLeft 
} from "lucide-react";
import Link from "next/link";

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error);
  }, [error]);

  const getErrorMessage = (error: Error) => {
    // Friendly error messages based on error type
    if (error.message.includes("fetch")) {
      return "La nostra AI medica sta avendo problemi di connessione. Riprova tra un momento!";
    }
    if (error.message.includes("timeout")) {
      return "Il nostro dottore AI sta prendendo una pausa caffè. Riprova tra poco!";
    }
    if (error.message.includes("payment")) {
      return "C'è stato un piccolo intoppo con il pagamento. Non preoccuparti, riprova!";
    }
    return "Qualcosa è andato storto, ma niente panico! I nostri esperti stanno già lavorando per risolvere.";
  };

  const getErrorTitle = (error: Error) => {
    if (error.message.includes("fetch") || error.message.includes("network")) {
      return "Problemi di Connessione";
    }
    if (error.message.includes("payment")) {
      return "Errore di Pagamento";
    }
    return "Oops! Qualcosa è Andato Storto";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-2xl mx-auto text-center space-y-8"
        >
          {/* Animated Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="relative mx-auto w-24 h-24"
          >
            <motion.div
              animate={{ 
                rotate: [0, -10, 10, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute inset-0 bg-gradient-to-br from-warning/20 to-destructive/20 rounded-full flex items-center justify-center"
            >
              <AlertTriangle className="w-12 h-12 text-warning" />
            </motion.div>
          </motion.div>

          {/* Error Message */}
          <div className="space-y-4">
            <Badge variant="outline" className="mb-4 border-warning text-warning">
              Errore Temporaneo
            </Badge>
            <h1 className="text-3xl sm:text-4xl font-bold text-foreground">
              {getErrorTitle(error)}
            </h1>
            <p className="text-lg text-muted-foreground leading-relaxed">
              {getErrorMessage(error)}
            </p>
          </div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button 
              onClick={reset}
              className="bg-gradient-to-r from-primary to-primary-dark"
              size="lg"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Riprova
            </Button>
            <Link href="/">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                <Home className="w-4 h-4 mr-2" />
                Torna alla Home
              </Button>
            </Link>
          </motion.div>

          {/* Alternative Actions */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="grid sm:grid-cols-2 gap-4 mt-8"
          >
            <Card className="hover:shadow-lg transition-all duration-300">
              <CardHeader className="text-center pb-4">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="mx-auto w-10 h-10 bg-secondary-accent/10 rounded-full flex items-center justify-center mb-2"
                >
                  <Stethoscope className="w-5 h-5 text-secondary-accent" />
                </motion.div>
                <CardTitle className="text-base">Test AI Scabbia</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-3 text-sm">
                  Prova il nostro test AI per €1
                </p>
                <Link href="/scabbia-checker">
                  <Button variant="outline" size="sm" className="w-full">
                    Vai al Test
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300">
              <CardHeader className="text-center pb-4">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="mx-auto w-10 h-10 bg-success/10 rounded-full flex items-center justify-center mb-2"
                >
                  <Heart className="w-5 h-5 text-success" />
                </motion.div>
                <CardTitle className="text-base">Informazioni</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-3 text-sm">
                  Scopri sintomi e cure
                </p>
                <Link href="/sintomi">
                  <Button variant="outline" size="sm" className="w-full">
                    Leggi di Più
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>

          {/* Technical Details (Development Only) */}
          {process.env.NODE_ENV === "development" && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="mt-8"
            >
              <Card className="bg-muted/50 border-muted">
                <CardHeader>
                  <CardTitle className="text-sm text-muted-foreground">
                    Dettagli Tecnici (Solo Sviluppo)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs text-muted-foreground overflow-auto">
                    {error.message}
                    {error.digest && `\nDigest: ${error.digest}`}
                  </pre>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.6 }}
            className="pt-4"
          >
            <Button 
              variant="ghost" 
              onClick={() => window.history.back()}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Torna Indietro
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
