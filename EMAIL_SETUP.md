# Email Configuration Setup

## Overview
This document explains how to configure email functionality for the Scabbia Sintomi contact form using IONOS email hosting.

## Environment Variables Required

Add these variables to your `.env.local` file:

```bash
# IONOS Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-actual-email-password

# IMAP Configuration (for receiving emails)
EMAIL_IMAP_HOST=imap.ionos.it
EMAIL_IMAP_PORT=993
EMAIL_IMAP_SECURE=true

# SMTP Configuration (for sending emails)
EMAIL_SMTP_HOST=smtp.ionos.it
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=false
EMAIL_SMTP_TLS=true
EMAIL_SMTP_AUTH_REQUIRED=true

# Email service configuration
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Scabbia Sintomi
EMAIL_REPLY_TO=<EMAIL>
```

## How It Works

### Contact Form Flow
1. User fills out contact form on `/contatti` page
2. Form data is validated client-side and server-side
3. Two emails are sent simultaneously:
   - **Admin notification** to `<EMAIL>` with the user's message
   - **User confirmation** to the user's email address

### Email Templates
- **Admin Email**: Contains all form data (name, email, subject, message) in a professional HTML template
- **User Confirmation**: Sends a branded confirmation email with contact information and response time expectations

### Security Features
- Rate limiting (prevents spam)
- Input validation and sanitization
- CSRF protection
- Secure headers
- Email configuration validation

## Files Modified/Created

### New Files
- `lib/email.ts` - Email utility functions and templates
- `app/api/contact/route.ts` - API endpoint for contact form
- `EMAIL_SETUP.md` - This documentation

### Modified Files
- `app/components/ContactForm.tsx` - Updated to use real API
- `app/components/Footer.tsx` - Updated with correct contact information
- `app/contatti/page.tsx` - Updated contact information
- `lib/security.ts` - Added email environment validation
- `.env.example` - Added email configuration variables

## Contact Information Updated

The following contact information has been updated throughout the application:

**Company**: Ruberto Go  
**Address**: Via Giuseppe Maggi 10, 6963 Lugano, Svizzera  
**Email**: <EMAIL>  
**IDI**: CHE-327.816.852  

## Testing

To test the email functionality:

1. Ensure all environment variables are set in `.env.local`
2. Start the development server: `npm run dev`
3. Navigate to `/contatti`
4. Fill out and submit the contact form
5. Check both:
   - Your admin email (<EMAIL>) for the notification
   - The user's email for the confirmation

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check environment variables are correctly set
   - Verify IONOS email credentials
   - Check server logs for detailed error messages

2. **Authentication errors**
   - Ensure EMAIL_PASSWORD is correct
   - Verify EMAIL_USER matches your IONOS email account

3. **Connection errors**
   - Check SMTP settings (host, port, TLS)
   - Ensure firewall allows outbound connections on port 587

### Error Logging
All email operations are logged with detailed information:
- Success/failure status
- Timestamp
- User information (for successful sends)
- Error details (for failures)

## Security Considerations

- Email passwords are stored as environment variables (never in code)
- All form inputs are validated and sanitized
- Rate limiting prevents abuse
- SMTP uses TLS encryption
- No sensitive information is logged

## Dependencies Added

```bash
npm install nodemailer @types/nodemailer
```

## Production Deployment

Before deploying to production:

1. Set all email environment variables in your hosting platform
2. Test email functionality in staging environment
3. Monitor email delivery rates
4. Set up email monitoring/alerting if needed

## Support

For email configuration issues:
- Check IONOS documentation
- Verify email account settings in IONOS control panel
- Contact IONOS support if needed
