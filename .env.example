# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Stripe configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PRODUCT_ID=prod_your_product_id
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# AI configuration
AI_MODEL=gpt-4.1-nano
OPENAI_API_KEY=sk-your-openai-api-key

# Security configuration (optional)
# Set to 'production' in production environment
NODE_ENV=development

# Database encryption key (generate with: openssl rand -hex 32)
# DATABASE_ENCRYPTION_KEY=your-32-byte-hex-key

# Session secret (generate with: openssl rand -hex 64)
# SESSION_SECRET=your-64-byte-hex-secret

# CORS allowed origins (comma-separated)
# ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate limiting configuration
# RATE_LIMIT_WINDOW_MS=60000
# RATE_LIMIT_MAX_REQUESTS=10

# Security headers configuration
# ENABLE_SECURITY_HEADERS=true
# CSP_REPORT_URI=https://yourdomain.com/api/csp-report

# IONOS Email Configuration
# Email account credentials
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password

# IMAP Configuration (Recommended for receiving emails)
EMAIL_IMAP_HOST=imap.ionos.it
EMAIL_IMAP_PORT=993
EMAIL_IMAP_SECURE=true

# SMTP Configuration (For sending emails)
EMAIL_SMTP_HOST=smtp.ionos.it
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=false
EMAIL_SMTP_TLS=true
EMAIL_SMTP_AUTH_REQUIRED=true

# POP3 Configuration (Alternative to IMAP)
# EMAIL_POP3_HOST=pop.ionos.it
# EMAIL_POP3_PORT=995
# EMAIL_POP3_SECURE=true

# Email service configuration
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Scabbia Sintomi
EMAIL_REPLY_TO=<EMAIL>
