import { NextResponse, NextRequest } from "next/server";
import {
  validateInput,
  getClientIp,
  getSecureHeaders,
  logSecurityEvent,
  apiRateLimiter,
  ValidationSchema,
  sanitizeHtml
} from "@/lib/security";
import { sendContactEmails, validateEmailConfig } from "@/lib/email";

// Input validation schema for contact form
const contactSchema: ValidationSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 2,
    maxLength: 100
  },
  email: {
    required: true,
    type: 'string',
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  subject: {
    required: false,
    type: 'string',
    maxLength: 200
  },
  message: {
    required: true,
    type: 'string',
    minLength: 10,
    maxLength: 2000
  },
  privacy: {
    required: true,
    type: 'boolean'
  }
};

export async function POST(request: NextRequest) {
  const secureHeaders = getSecureHeaders();
  const clientIp = getClientIp(request);
  const requestId = Math.random().toString(36).substring(2, 9);

  console.log(`[${new Date().toISOString()}] [${requestId}] Contact form submission from IP: ${clientIp}`);

  // Rate limiting
  if (!apiRateLimiter.isAllowed(clientIp)) {
    logSecurityEvent('Contact form rate limit exceeded', { clientIp, endpoint: '/api/contact' });
    return NextResponse.json(
      {
        error: "Troppe richieste. Riprova più tardi.",
        retryAfter: Math.ceil((apiRateLimiter.getResetTime(clientIp) - Date.now()) / 1000)
      },
      {
        status: 429,
        headers: {
          ...secureHeaders,
          'Retry-After': '60',
          'X-RateLimit-Remaining': apiRateLimiter.getRemainingRequests(clientIp).toString()
        }
      }
    );
  }

  // Validate email configuration
  const emailConfigValidation = validateEmailConfig();
  if (!emailConfigValidation.isValid) {
    console.error(`[${requestId}] Email configuration invalid:`, emailConfigValidation.errors);
    logSecurityEvent('Email configuration invalid', {
      errors: emailConfigValidation.errors,
      clientIp
    });
    return NextResponse.json(
      { error: "Configurazione email del server non valida" },
      { status: 500, headers: secureHeaders }
    );
  }

  try {
    const body = await request.json();

    // Validate and sanitize input
    const validation = validateInput(body, contactSchema);
    if (!validation.isValid) {
      logSecurityEvent('Invalid contact form data', {
        errors: validation.errors,
        clientIp
      });
      return NextResponse.json(
        {
          error: "Dati non validi",
          details: validation.errors
        },
        { status: 400, headers: secureHeaders }
      );
    }

    const { name, email, subject, message, privacy } = validation.sanitizedData;

    // Check privacy acceptance
    if (!privacy) {
      return NextResponse.json(
        { error: "Devi accettare la privacy policy per continuare" },
        { status: 400, headers: secureHeaders }
      );
    }

    // Sanitize message content
    const sanitizedMessage = sanitizeHtml(message as string);

    // Prepare email data
    const emailData = {
      name: name as string,
      email: email as string,
      subject: subject as string || '',
      message: sanitizedMessage
    };

    console.log(`[${requestId}] Sending emails for contact form submission from ${name} (${email})`);

    // Send emails
    const { adminSent, userSent } = await sendContactEmails(emailData);

    // Log results
    if (adminSent && userSent) {
      console.log(`[${requestId}] Both emails sent successfully`);
      logSecurityEvent('Contact form emails sent successfully', {
        name,
        email,
        clientIp,
        adminSent,
        userSent
      });
    } else {
      console.error(`[${requestId}] Email sending failed - Admin: ${adminSent}, User: ${userSent}`);
      logSecurityEvent('Contact form email sending failed', {
        name,
        email,
        clientIp,
        adminSent,
        userSent
      });
    }

    // Return success even if user confirmation fails (admin notification is more important)
    if (adminSent) {
      return NextResponse.json(
        {
          success: true,
          message: "Messaggio inviato con successo! Ti risponderemo il prima possibile.",
          details: {
            adminNotified: adminSent,
            confirmationSent: userSent
          }
        },
        { headers: secureHeaders }
      );
    } else {
      // If admin notification fails, return error
      return NextResponse.json(
        { error: "Errore durante l'invio del messaggio. Riprova più tardi." },
        { status: 500, headers: secureHeaders }
      );
    }

  } catch (error) {
    console.error(`[${requestId}] Contact form error:`, error);
    logSecurityEvent('Contact form processing failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      clientIp
    });
    return NextResponse.json(
      { error: "Errore interno del server. Riprova più tardi." },
      { status: 500, headers: secureHeaders }
    );
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { error: "Metodo non consentito" },
    { status: 405, headers: getSecureHeaders() }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: "Metodo non consentito" },
    { status: 405, headers: getSecureHeaders() }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Metodo non consentito" },
    { status: 405, headers: getSecureHeaders() }
  );
}
