import nodemailer from 'nodemailer';

// Email configuration interface
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  tls?: {
    rejectUnauthorized: boolean;
  };
}

// Contact form data interface
interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Create email transporter
function createTransporter(): nodemailer.Transporter {
  const config: EmailConfig = {
    host: process.env.EMAIL_SMTP_HOST || 'smtp.ionos.it',
    port: parseInt(process.env.EMAIL_SMTP_PORT || '587'),
    secure: process.env.EMAIL_SMTP_SECURE === 'true', // false for 587, true for 465
    auth: {
      user: process.env.EMAIL_USER || '',
      pass: process.env.EMAIL_PASSWORD || '',
    },
  };

  // Add TLS configuration for port 587
  if (process.env.EMAIL_SMTP_TLS === 'true') {
    config.tls = {
      rejectUnauthorized: false
    };
  }

  return nodemailer.createTransporter(config);
}

// Email template for admin notification
function getAdminEmailTemplate(data: ContactFormData): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Nuovo messaggio dal sito Scabbia Sintomi</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #555; }
        .value { background: white; padding: 10px; border-radius: 4px; border-left: 4px solid #667eea; }
        .message-box { background: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>🩺 Nuovo messaggio dal sito Scabbia Sintomi</h2>
        </div>
        <div class="content">
          <div class="field">
            <div class="label">Nome:</div>
            <div class="value">${data.name}</div>
          </div>
          <div class="field">
            <div class="label">Email:</div>
            <div class="value">${data.email}</div>
          </div>
          <div class="field">
            <div class="label">Oggetto:</div>
            <div class="value">${data.subject || 'Nessun oggetto specificato'}</div>
          </div>
          <div class="field">
            <div class="label">Messaggio:</div>
            <div class="message-box">${data.message.replace(/\n/g, '<br>')}</div>
          </div>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
          <p style="font-size: 12px; color: #666;">
            Messaggio ricevuto il ${new Date().toLocaleString('it-IT')} dal sito scabbiasintomi.it
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Email template for user confirmation
function getUserConfirmationTemplate(name: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Conferma ricezione messaggio - Scabbia Sintomi</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .message { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
        .contact-info { background: white; padding: 15px; border-radius: 8px; margin-top: 15px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">🩺 Scabbia Sintomi</div>
          <p>Conferma ricezione messaggio</p>
        </div>
        <div class="content">
          <div class="message">
            <h3>Ciao ${name},</h3>
            <p>Grazie per averci contattato! Abbiamo ricevuto il tuo messaggio e ti risponderemo il prima possibile.</p>
            <p><strong>Tempi di risposta:</strong> Normalmente rispondiamo entro 24-48 ore lavorative.</p>
            <p>Se la tua richiesta è urgente, puoi contattarci direttamente via email all'indirizzo <EMAIL></p>
          </div>
          
          <div class="contact-info">
            <h4>📞 I nostri contatti:</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Sede legale:</strong> Ruberto Go<br>
            Via Giuseppe Maggi 10<br>
            6963 Lugano, Svizzera<br>
            IDI: CHE-327.816.852</p>
          </div>
          
          <div class="footer">
            <p>Questo è un messaggio automatico, non rispondere a questa email.</p>
            <p>© ${new Date().getFullYear()} Scabbia Sintomi - Tutti i diritti riservati</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Send email to admin
export async function sendAdminNotification(data: ContactFormData): Promise<boolean> {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME || 'Scabbia Sintomi'}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: process.env.EMAIL_USER || '<EMAIL>',
      subject: `[Scabbia Sintomi] Nuovo messaggio da ${data.name}`,
      html: getAdminEmailTemplate(data),
      replyTo: data.email,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending admin notification:', error);
    return false;
  }
}

// Send confirmation email to user
export async function sendUserConfirmation(data: ContactFormData): Promise<boolean> {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME || 'Scabbia Sintomi'}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: data.email,
      subject: 'Conferma ricezione messaggio - Scabbia Sintomi',
      html: getUserConfirmationTemplate(data.name),
      replyTo: process.env.EMAIL_REPLY_TO || process.env.EMAIL_USER,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending user confirmation:', error);
    return false;
  }
}

// Send both emails
export async function sendContactEmails(data: ContactFormData): Promise<{ adminSent: boolean; userSent: boolean }> {
  const [adminSent, userSent] = await Promise.all([
    sendAdminNotification(data),
    sendUserConfirmation(data)
  ]);

  return { adminSent, userSent };
}

// Validate email configuration
export function validateEmailConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!process.env.EMAIL_USER) {
    errors.push('EMAIL_USER is required');
  }

  if (!process.env.EMAIL_PASSWORD || process.env.EMAIL_PASSWORD === 'your-ionos-email-password') {
    errors.push('EMAIL_PASSWORD must be configured with actual password');
  }

  if (!process.env.EMAIL_SMTP_HOST) {
    errors.push('EMAIL_SMTP_HOST is required');
  }

  if (!process.env.EMAIL_SMTP_PORT) {
    errors.push('EMAIL_SMTP_PORT is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
