import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Bug, Users, Shield, AlertTriangle, Home, Plane, CheckCircle, Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Cause della Scabbia | Come si Prende - ScabbiaSintomi.it',
  description: 'Scopri le principali cause della scabbia, come avviene il contagio e quali sono i fattori di rischio. Guida completa per la prevenzione.',
  keywords: 'cause scabbia, contagio, trasmissione, fattori rischio, Sarcoptes scabiei',
};

const transmissionMethods = [
  {
    icon: Users,
    title: 'Contatto diretto',
    description: 'La modalità più comune di trasmissione è il contatto pelle a pelle prolungato',
    methods: ['Abbracci', 'Contatto sessuale', 'Do<PERSON><PERSON> nello stesso letto', 'Contatto fisico prolungato'],
    risk: 'Alto'
  },
  {
    icon: Home,
    title: 'Contatto indiretto',
    description: 'Meno frequentemente, attraverso il contatto con oggetti contaminati',
    methods: ['Biancheria da letto infetta', 'Asciugamani', 'Vestiti', 'Mobili imbottiti'],
    risk: 'Basso'
  }
];

const riskFactors = [
  { title: 'Vita comunitaria', icon: Home, desc: 'Vivere in ambienti affollati come case di cura, dormitori o carceri' },
  { title: 'Età avanzata', icon: Users, desc: 'Anziani in strutture di assistenza' },
  { title: 'Sistema immunitario', icon: Shield, desc: 'Persone con sistema immunitario indebolito' },
  { title: 'Contatti frequenti', icon: Users, desc: 'Persone con molti contatti fisici (es. operatori sanitari)' },
  { title: 'Condizioni igieniche', icon: AlertTriangle, desc: 'Scarse condizioni igieniche' },
  { title: 'Viaggi', icon: Plane, desc: 'Viaggi in aree con scarse condizioni igieniche' },
];

const myths = [
  "La scabbia non è causata dalla scarsa igiene personale. Chiunque può prenderla, indipendentemente dal proprio livello di pulizia.",
  "Gli animali domestici non trasmettono la scabbia umana. Hanno i loro tipi di acari che non sopravvivono a lungo sulla pelle umana.",
  "La scabbia non è un'infezione batterica o virale, quindi gli antibiotici non sono efficaci contro di essa.",
  "Non è necessario disinfestare tutta la casa. Basta lavare la biancheria e i vestiti a temperatura elevata."
];

export default function Cause() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Cause e Trasmissione
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Cause</span> della Scabbia
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Comprendi come si trasmette la scabbia e quali sono i fattori di rischio
                per una prevenzione efficace.
              </p>
            </div>
          </div>
        </section>

        {/* Agent Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <Bug className="w-6 h-6 text-primary" />
                    L&apos;agente responsabile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    La scabbia è causata da un acaro microscopico chiamato <em className="text-foreground font-medium">Sarcoptes scabiei</em> var. <em className="text-foreground font-medium">hominis</em>.
                    Questo parassita si insedia nello strato superiore della pelle, dove depone le uova e rilascia sostanze
                    che causano una reazione allergica, responsabile del prurito intenso.
                  </p>
                </CardContent>
              </Card>

              {/* Transmission Methods */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Come si trasmette
                  </h2>
                  <p className="text-muted-foreground">
                    Le modalità di contagio della scabbia e i relativi livelli di rischio
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {transmissionMethods.map((method, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                            <method.icon className="w-6 h-6 text-white" />
                          </div>
                          <Badge variant={method.risk === 'Alto' ? 'destructive' : 'secondary'}>
                            Rischio {method.risk}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-foreground">
                          {method.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-muted-foreground leading-relaxed">
                          {method.description}
                        </p>
                        <div className="space-y-2">
                          {method.methods.map((item, idx) => (
                            <div key={idx} className="flex items-center gap-3 p-2 bg-background/50 rounded-lg">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              <span className="text-foreground text-sm">{item}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Card className="border-l-4 border-l-warning bg-warning-light/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <Info className="w-6 h-6 text-warning flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="font-semibold text-foreground mb-2">
                          Informazione importante
                        </h3>
                        <p className="text-muted-foreground">
                          Gli acari della scabbia possono sopravvivere fuori dal corpo umano per 24-36 ore.
                          Tuttavia, la trasmissione attraverso oggetti è meno comune della trasmissione da contatto diretto.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Risk Factors */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Fattori di rischio
                  </h2>
                  <p className="text-muted-foreground">
                    Situazioni e condizioni che aumentano la probabilità di contagio
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {riskFactors.map((factor, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-secondary-accent to-primary flex items-center justify-center shadow-medium mb-4">
                          <factor.icon className="w-6 h-6 text-white" />
                        </div>
                        <CardTitle className="text-lg text-foreground">
                          {factor.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {factor.desc}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Myths Section */}
              <Card className="border-0 bg-success-light/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <CheckCircle className="w-6 h-6 text-success" />
                    Miti da sfatare
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4">
                    {myths.map((myth, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-background/80 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-success flex-shrink-0 mt-0.5" />
                        <span className="text-foreground leading-relaxed">{myth}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Vuoi saperne di più?
                </h3>
                <p className="text-muted-foreground">
                  Scopri come prevenire il contagio e proteggere te stesso e la tua famiglia.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/prevenzione">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Scopri la Prevenzione
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/scabbia-checker">
                    <Button variant="outline" size="lg">
                      Test AI €1
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
