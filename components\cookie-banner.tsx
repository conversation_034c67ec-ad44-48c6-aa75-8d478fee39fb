'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, <PERSON>tings, X, Check } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  hasGivenConsent,
  saveCookieConsent,
  acceptAllCookies,
  acceptEssentialOnly,
  getCookieCategories,
  getCookieConsent,
} from '@/lib/cookie-consent';
import { CookieConsent, defaultCookieConsent } from '@/types/cookie-consent';

export function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState<CookieConsent>(defaultCookieConsent);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Check if user has already given consent
    if (!hasGivenConsent()) {
      // Show banner after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    // Load existing preferences if any
    const existingConsent = getCookieConsent();
    if (existingConsent) {
      setPreferences(existingConsent.consent);
    }
  }, []);

  const handleAcceptAll = () => {
    acceptAllCookies();
    setIsVisible(false);
  };

  const handleAcceptEssential = () => {
    acceptEssentialOnly();
    setIsVisible(false);
  };

  const handleSavePreferences = () => {
    saveCookieConsent(preferences);
    setIsVisible(false);
    setShowPreferences(false);
  };

  const handlePreferenceChange = (key: keyof CookieConsent, value: boolean) => {
    if (key === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const cookieCategories = getCookieCategories();

  // Don't render on server or if user has already consented
  if (!mounted || !isVisible) {
    return null;
  }

  return (
    <>
      {/* Cookie Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur-sm border-t shadow-lg">
        <div className="container-custom">
          <Card className="border-0 shadow-medium bg-card/90 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                <div className="flex items-start gap-3 flex-1">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <Cookie className="w-4 h-4 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-foreground">
                      Utilizziamo i Cookie
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Utilizziamo cookie tecnici necessari per il funzionamento del sito e cookie analitici 
                      per migliorare la tua esperienza. Puoi gestire le tue preferenze o accettare tutti i cookie.
                    </p>
                    <div className="flex flex-wrap gap-2 text-xs">
                      <Link 
                        href="/privacy" 
                        className="text-primary hover:text-primary-dark underline"
                      >
                        Privacy Policy
                      </Link>
                      <span className="text-muted-foreground">•</span>
                      <Link 
                        href="/cookie" 
                        className="text-primary hover:text-primary-dark underline"
                      >
                        Cookie Policy
                      </Link>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 lg:flex-shrink-0">
                  <Dialog open={showPreferences} onOpenChange={setShowPreferences}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" className="flex items-center gap-2">
                        <Settings className="w-4 h-4" />
                        Gestisci Preferenze
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <Cookie className="w-5 h-5 text-primary" />
                          Preferenze Cookie
                        </DialogTitle>
                        <DialogDescription>
                          Scegli quali categorie di cookie accettare. I cookie tecnici sono sempre attivi 
                          perché necessari per il funzionamento del sito.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        {cookieCategories.map((category) => (
                          <Card key={category.key} className="border">
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <div className="space-y-1">
                                  <CardTitle className="text-base flex items-center gap-2">
                                    {category.name}
                                    {category.required && (
                                      <Badge variant="secondary" className="text-xs">
                                        Necessari
                                      </Badge>
                                    )}
                                  </CardTitle>
                                  <CardDescription className="text-sm">
                                    {category.description}
                                  </CardDescription>
                                </div>
                                <Switch
                                  checked={preferences[category.key]}
                                  onCheckedChange={(checked) => 
                                    handlePreferenceChange(category.key, checked)
                                  }
                                  disabled={category.required}
                                />
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className="text-xs text-muted-foreground">
                                <strong>Esempi:</strong> {category.examples.join(', ')}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                      
                      <div className="flex flex-col sm:flex-row gap-2 pt-4">
                        <Button onClick={handleSavePreferences} className="flex-1">
                          <Check className="w-4 h-4 mr-2" />
                          Salva Preferenze
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => setShowPreferences(false)}
                          className="flex-1"
                        >
                          Annulla
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                  
                  <Button variant="outline" size="sm" onClick={handleAcceptEssential}>
                    Solo Necessari
                  </Button>
                  <Button size="sm" onClick={handleAcceptAll} className="bg-primary hover:bg-primary-dark">
                    Accetta Tutti
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
