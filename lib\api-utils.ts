import { toast } from "react-hot-toast";
import { useState } from "react";
import { processApiError, logError, shouldRetry, getRetryDelay, formatErrorForToast, ApiError } from "./api-error-handler";

// Define types for our API request/response
type ApiResponse = {
  answer: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
};

// Type for chat-based inputs
type ChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

type ApiInput = { 
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
};

// Rate limiting configuration
const RATE_LIMIT_REQUESTS = 10; // Max 10 requests
const RATE_LIMIT_INTERVAL = 60 * 1000; // Per minute

// Track API calls
let requestQueue: number[] = [];

// Clear old requests from queue
const cleanupQueue = () => {
  const now = Date.now();
  requestQueue = requestQueue.filter(timestamp => now - timestamp < RATE_LIMIT_INTERVAL);
};

// Check if request is allowed
const isRequestAllowed = (): boolean => {
  cleanupQueue();
  return requestQueue.length < RATE_LIMIT_REQUESTS;
};

// Add request to queue
const trackRequest = () => {
  requestQueue.push(Date.now());
};

export type ApiInputData = Record<string, unknown>;

export const callOpenAI = async (input: ApiInputData): Promise<{ data?: ApiResponse; error?: ApiError }> => {
  // Transform the input to match the expected API format
  const apiInput: ApiInput = {
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: JSON.stringify(input) },
    ],
    temperature: 0.7,
    max_tokens: 1000,
  };

  try {
    // Check rate limit
    if (!isRequestAllowed()) {
      const rateLimitError = processApiError({ code: 'RATE_LIMIT_EXCEEDED', status: 429 });
      logError(rateLimitError, { input: 'API call blocked by rate limit' });
      return { error: rateLimitError };
    }

    trackRequest();

    const response = await fetch("/api/ask-ai", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ input: apiInput }),
    });

    const data = await response.json();

    if (!response.ok) {
      // Process the error using our centralized error handler
      const apiError = processApiError({
        status: response.status,
        message: data.error || `HTTP ${response.status}`,
        code: data.code
      });

      logError(apiError, {
        url: "/api/ask-ai",
        status: response.status,
        response: data
      });

      return { error: apiError };
    }

    return { data };
  } catch (error) {
    // Process network/connection errors
    const apiError = processApiError(error);
    logError(apiError, {
      url: "/api/ask-ai",
      originalError: error
    });

    return { error: apiError };
  }
};

// Utility to show error toast with enhanced formatting
const showError = (error: ApiError) => {
  const message = formatErrorForToast(error);
  toast.error(message, {
    position: 'top-center',
    duration: error.severity === 'high' ? 8000 : 5000,
    style: {
      maxWidth: '400px',
    },
  });
};

// Enhanced hook for components to use the API with retry logic
export const useOpenAI = (): {
  callAPI: (input: ApiInputData) => Promise<ApiResponse | null>;
  isLoading: boolean;
  error: ApiError | null;
  retryCount: number;
  canRetry: boolean;
} => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const callAPI = async (input: ApiInputData, attemptCount: number = 0): Promise<ApiResponse | null> => {
    setIsLoading(true);
    setError(null);
    setRetryCount(attemptCount);

    try {
      const { data, error } = await callOpenAI(input);

      if (error) {
        // Check if we should retry
        if (shouldRetry(error, attemptCount)) {
          const delay = getRetryDelay(error, attemptCount);

          // Show a different message for retries
          if (attemptCount > 0) {
            toast.loading(`Tentativo ${attemptCount + 1}... Riprovo tra ${delay / 1000}s`, {
              duration: delay,
            });
          }

          // Wait and retry
          setTimeout(() => {
            callAPI(input, attemptCount + 1);
          }, delay);

          return null;
        }

        // No more retries, show error
        setError(error);
        showError(error);
        return null;
      }

      if (!data) {
        return null;
      }

      // Reset retry count on success
      setRetryCount(0);
      return data;
    } catch (err) {
      const apiError = processApiError(err);
      setError(apiError);
      showError(apiError);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const canRetry = error ? shouldRetry(error, retryCount) : false;

  return { callAPI, isLoading, error, retryCount, canRetry };
};
