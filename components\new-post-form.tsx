"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import toast from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Save, CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

function slugify(text: string) {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, "")
    .replace(/\s+/g, "-");
}

export function NewPostForm() {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [excerpt, setExcerpt] = useState("");
  const [content, setContent] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    // Show loading toast with enhanced styling
    const loadingToast = toast.loading("Salvataggio articolo in corso...", {
      duration: Infinity,
      style: {
        background: 'hsl(var(--card))',
        color: 'hsl(var(--card-foreground))',
        border: '1px solid hsl(var(--border))',
      },
    });

    try {
      const finalSlug = slug || slugify(title);

      const res = await fetch("/api/posts/new", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ title, slug: finalSlug, excerpt, content }),
      });

      if (!res.ok) {
        const data = await res.json();
        throw new Error(data.error || "Errore durante il salvataggio");
      }

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Articolo salvato con successo!", {
        icon: "✅",
        duration: 3000,
        style: {
          background: 'hsl(var(--card))',
          color: 'hsl(var(--card-foreground))',
          border: '1px solid hsl(var(--border))',
        },
      });

      // Set success state for visual feedback
      setSuccess(true);

      // Redirect after a brief delay to show success state
      setTimeout(() => {
        router.push("/blog/" + finalSlug);
      }, 1000);

    } catch (err) {
      // Dismiss loading toast and show error
      toast.dismiss(loadingToast);

      const errorMessage = err instanceof Error ? err.message : "Errore durante il salvataggio";
      setError(errorMessage);

      toast.error(errorMessage, {
        icon: "❌",
        duration: 5000,
        style: {
          background: 'hsl(var(--card))',
          color: 'hsl(var(--card-foreground))',
          border: '1px solid hsl(var(--destructive))',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative">
      {/* Loading Overlay */}
      <AnimatePresence>
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-xl"
          >
            <div className="flex items-center gap-3 bg-card p-4 rounded-lg shadow-lg border">
              <Loader2 className="w-5 h-5 animate-spin text-primary" />
              <span className="text-sm font-medium">Salvataggio in corso...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Card className={cn(
        "transition-all duration-300",
        loading && "pointer-events-none",
        success && "border-green-200 bg-green-50/50"
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {success ? (
              <>
                <CheckCircle className="w-5 h-5 text-green-600" />
                Articolo Salvato
              </>
            ) : (
              <>
                <Save className="w-5 h-5" />
                Nuovo Articolo
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Titolo</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  if(!slug) setSlug(slugify(e.target.value));
                }}
                required
                disabled={loading}
                placeholder="Inserisci il titolo dell'articolo..."
                className={cn(
                  "transition-all duration-200",
                  loading && "opacity-60"
                )}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                required
                disabled={loading}
                placeholder="url-dell-articolo"
                className={cn(
                  "transition-all duration-200",
                  loading && "opacity-60"
                )}
              />
              <p className="text-xs text-muted-foreground">
                L'URL dell'articolo. Viene generato automaticamente dal titolo.
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="excerpt">Estratto</Label>
              <Input
                id="excerpt"
                value={excerpt}
                onChange={(e) => setExcerpt(e.target.value)}
                disabled={loading}
                placeholder="Breve descrizione dell'articolo (opzionale)..."
                className={cn(
                  "transition-all duration-200",
                  loading && "opacity-60"
                )}
              />
            </div>

            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="content">Contenuto (Markdown)</Label>
                <span className="text-xs text-muted-foreground">
                  {content.length} caratteri
                </span>
              </div>
              <Textarea
                id="content"
                className={cn(
                  "min-h-40 transition-all duration-200",
                  loading && "opacity-60"
                )}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                required
                disabled={loading}
                placeholder="Scrivi il contenuto del tuo articolo in formato Markdown..."
              />
            </div>

            {/* Error Message */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md"
                >
                  <AlertCircle className="w-4 h-4 text-red-600 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Success Message */}
            <AnimatePresence>
              {success && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md"
                >
                  <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                  <p className="text-sm text-green-700">Articolo salvato con successo! Reindirizzamento in corso...</p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={loading || success}
              className={cn(
                "transition-all duration-300",
                success && "bg-green-600 hover:bg-green-700"
              )}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Salvataggio...
                </>
              ) : success ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Salvato!
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Salva
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
