import { NextResponse, NextRequest } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { marked } from "marked";
import {
  validateInput,
  getClientIp,
  getSecureHeaders,
  logSecurityEvent,
  authRateLimiter,
  ValidationSchema,
  sanitizeHtml
} from "@/lib/security";

// Input validation schema for blog posts
const postSchema: ValidationSchema = {
  title: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 200
  },
  slug: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-z0-9-]+$/
  },
  excerpt: {
    required: false,
    type: 'string',
    maxLength: 500
  },
  content: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 50000
  }
};

export async function POST(request: Request) {
  const secureHeaders = getSecureHeaders();
  const clientIp = getClientIp(request as unknown as NextRequest);

  // Rate limiting for authenticated endpoints
  if (!authRateLimiter.isAllowed(clientIp)) {
    logSecurityEvent('Auth rate limit exceeded', { clientIp, endpoint: '/api/posts/new' });
    return NextResponse.json(
      { error: "Troppe richieste. Riprova più tardi." },
      { status: 429, headers: secureHeaders }
    );
  }

  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    logSecurityEvent('Unauthorized blog post creation attempt', { clientIp });
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401, headers: secureHeaders }
    );
  }

  try {
    const body = await request.json();

    // Validate and sanitize input
    const validation = validateInput(body, postSchema);
    if (!validation.isValid) {
      logSecurityEvent('Invalid blog post data', {
        errors: validation.errors,
        userId: user.id,
        clientIp
      });
      return NextResponse.json(
        {
          error: "Dati non validi",
          details: validation.errors
        },
        { status: 400, headers: secureHeaders }
      );
    }

    const { title, slug, excerpt, content } = validation.sanitizedData;

    // Additional security: Check for duplicate slugs
    const { data: existingPost } = await supabase
      .from("posts")
      .select("id")
      .eq("slug", slug)
      .single();

    if (existingPost) {
      return NextResponse.json(
        { error: "Slug già esistente" },
        { status: 409, headers: secureHeaders }
      );
    }

    // Sanitize and parse markdown content
    const sanitizedContent = sanitizeHtml(content as string);
    const html = marked.parse(sanitizedContent);

    const { error } = await supabase
      .from("posts")
      .insert({
        title,
        slug,
        excerpt: excerpt || null,
        content: html,
        author_id: user.id
      });

    if (error) {
      console.error('Database error creating post:', error);
      return NextResponse.json(
        { error: "Errore durante la creazione del post" },
        { status: 500, headers: secureHeaders }
      );
    }

    logSecurityEvent('Blog post created successfully', {
      userId: user.id,
      slug,
      clientIp
    });

    return NextResponse.json(
      { success: true },
      { headers: secureHeaders }
    );

  } catch (error) {
    console.error('Error processing blog post creation:', error);
    logSecurityEvent('Blog post creation error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: user.id,
      clientIp
    });

    return NextResponse.json(
      { error: "Errore durante l'elaborazione della richiesta" },
      { status: 500, headers: secureHeaders }
    );
  }
}
