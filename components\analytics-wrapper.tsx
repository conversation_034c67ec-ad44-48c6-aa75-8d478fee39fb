'use client';

import { useEffect } from 'react';
import { useCookieConsent } from '@/hooks/use-cookie-consent';

interface AnalyticsWrapperProps {
  children: React.ReactNode;
}

/**
 * Example component showing how to conditionally load analytics
 * based on cookie consent
 */
export function AnalyticsWrapper({ children }: AnalyticsWrapperProps) {
  const { hasConsentFor, isLoading } = useCookieConsent();

  useEffect(() => {
    if (isLoading) return;

    // Only load analytics if user has consented to analytics cookies
    if (hasConsentFor('analytics')) {
      // Initialize Google Analytics or other analytics tools here
      console.log('Analytics enabled - user has consented');
      
      // Example: Load Google Analytics
      // gtag('config', 'GA_MEASUREMENT_ID');
    } else {
      console.log('Analytics disabled - user has not consented');
      
      // Disable or remove analytics tracking
      // gtag('config', 'GA_MEASUREMENT_ID', { 'anonymize_ip': true });
    }
  }, [hasConsentFor, isLoading]);

  return <>{children}</>;
}
