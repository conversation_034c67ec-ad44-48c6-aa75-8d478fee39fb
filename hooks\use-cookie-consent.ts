'use client';

import { useState, useEffect } from 'react';
import { 
  CookieConsent, 
  CookieConsentState,
  defaultCookieConsent 
} from '@/types/cookie-consent';
import { 
  getCookieConsent, 
  hasConsentFor, 
  hasGivenConsent 
} from '@/lib/cookie-consent';

export function useCookieConsent() {
  const [consent, setConsent] = useState<CookieConsent>(defaultCookieConsent);
  const [hasConsented, setHasConsented] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load initial consent state
    const consentState = getCookieConsent();
    if (consentState) {
      setConsent(consentState.consent);
      setHasConsented(consentState.hasConsented);
    }
    setIsLoading(false);

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      const newConsentState = event.detail as CookieConsentState | null;
      if (newConsentState) {
        setConsent(newConsentState.consent);
        setHasConsented(newConsentState.hasConsented);
      } else {
        setConsent(defaultCookieConsent);
        setHasConsented(false);
      }
    };

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener);
    
    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener);
    };
  }, []);

  return {
    consent,
    hasConsented,
    isLoading,
    hasConsentFor: (cookieType: keyof CookieConsent) => hasConsentFor(cookieType),
    hasGivenConsent: () => hasGivenConsent(),
  };
}
