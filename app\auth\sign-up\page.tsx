import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, UserX } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Page() {
  return (
    <div className="min-h-screen bg-background">
      {/* Minimal Navigation */}
      <div className="absolute top-4 left-4 z-10">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Torna alla Home
        </Link>
      </div>

      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardHeader className="text-center">
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <UserX className="w-6 h-6 text-muted-foreground" />
              </div>
              <CardTitle className="text-2xl">Registrazione Non Disponibile</CardTitle>
              <CardDescription>
                Le nuove registrazioni sono temporaneamente sospese
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground text-center">
                Al momento non è possibile creare nuovi account. Se hai già un account,
                puoi accedere utilizzando le tue credenziali esistenti.
              </p>
              <div className="space-y-2">
                <Button asChild className="w-full">
                  <Link href="/auth/login">Accedi al tuo Account</Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/">Torna alla Home</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
