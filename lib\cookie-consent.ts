'use client';

import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>onsentState, 
  defaultC<PERSON><PERSON><PERSON>onsent, 
  COOKIE_CONSENT_KEY,
  COOKIE_CONSENT_VERSION 
} from '@/types/cookie-consent';

/**
 * Get cookie consent from localStorage
 */
export function getCookieConsent(): CookieConsentState | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!stored) return null;
    
    const parsed = JSON.parse(stored);
    
    // Check if the stored consent is for the current version
    if (parsed.version !== COOKIE_CONSENT_VERSION) {
      return null;
    }
    
    return parsed;
  } catch (error) {
    console.error('Error reading cookie consent:', error);
    return null;
  }
}

/**
 * Save cookie consent to localStorage
 */
export function saveCookieConsent(consent: CookieConsent): void {
  if (typeof window === 'undefined') return;
  
  const consentState: CookieConsentState & { version: string } = {
    hasConsented: true,
    consent: {
      ...consent,
      essential: true, // Always ensure essential cookies are enabled
    },
    timestamp: Date.now(),
    version: COOKIE_CONSENT_VERSION,
  };
  
  try {
    localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consentState));
    
    // Trigger custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: consentState
    }));
  } catch (error) {
    console.error('Error saving cookie consent:', error);
  }
}

/**
 * Check if user has given consent for a specific cookie type
 */
export function hasConsentFor(cookieType: keyof CookieConsent): boolean {
  const consent = getCookieConsent();
  if (!consent) return cookieType === 'essential'; // Only essential cookies by default
  
  return consent.consent[cookieType];
}

/**
 * Check if user has consented to cookies at all
 */
export function hasGivenConsent(): boolean {
  const consent = getCookieConsent();
  return consent?.hasConsented ?? false;
}

/**
 * Clear all cookie consent data
 */
export function clearCookieConsent(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(COOKIE_CONSENT_KEY);
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: null
    }));
  } catch (error) {
    console.error('Error clearing cookie consent:', error);
  }
}

/**
 * Accept all cookies
 */
export function acceptAllCookies(): void {
  saveCookieConsent({
    essential: true,
    analytics: true,
    preferences: true,
    marketing: true,
  });
}

/**
 * Accept only essential cookies
 */
export function acceptEssentialOnly(): void {
  saveCookieConsent(defaultCookieConsent);
}

/**
 * Get cookie categories with Italian descriptions
 */
export function getCookieCategories() {
  return [
    {
      key: 'essential' as keyof CookieConsent,
      name: 'Cookie Tecnici',
      description: 'Necessari per il funzionamento del sito web. Non possono essere disabilitati.',
      required: true,
      examples: ['Sessione utente', 'Sicurezza', 'Funzionalità di base']
    },
    {
      key: 'analytics' as keyof CookieConsent,
      name: 'Cookie Analitici',
      description: 'Ci aiutano a capire come i visitatori interagiscono con il sito web.',
      required: false,
      examples: ['Google Analytics', 'Statistiche di utilizzo', 'Miglioramento delle prestazioni']
    },
    {
      key: 'preferences' as keyof CookieConsent,
      name: 'Cookie di Preferenze',
      description: 'Memorizzano le tue preferenze per personalizzare l\'esperienza.',
      required: false,
      examples: ['Tema scuro/chiaro', 'Lingua preferita', 'Impostazioni di visualizzazione']
    },
    {
      key: 'marketing' as keyof CookieConsent,
      name: 'Cookie di Marketing',
      description: 'Utilizzati per mostrare contenuti pubblicitari pertinenti.',
      required: false,
      examples: ['Pubblicità mirata', 'Retargeting', 'Social media']
    }
  ];
}
