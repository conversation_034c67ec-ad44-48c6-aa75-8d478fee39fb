export interface CookieConsent {
  essential: boolean;
  analytics: boolean;
  preferences: boolean;
  marketing: boolean;
}

export interface CookieConsentState {
  hasConsented: boolean;
  consent: CookieConsent;
  timestamp: number;
}

export const defaultCookieConsent: CookieConsent = {
  essential: true, // Always true, cannot be disabled
  analytics: false,
  preferences: false,
  marketing: false,
};

export const COOKIE_CONSENT_KEY = 'scabbia-sintomi-cookie-consent';
export const COOKIE_CONSENT_VERSION = '1.0';
