import Link from 'next/link'
import { ArrowR<PERSON>, Shield, Clock, CheckCircle, Star } from 'lucide-react'
import { Button } from '@/components/ui/button'

const features = [
  {
    icon: Clock,
    text: "Risultati in 60 secondi"
  },
  {
    icon: Shield,
    text: "Test medico affidabile"
  },
  {
    icon: CheckCircle,
    text: "Consigli personalizzati"
  }
]

const trustIndicators = [
  "Oltre 10.000 test completati",
  "Validato da professionisti medici",
  "Informazioni sempre aggiornate"
]

export default function Hero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary-light via-background to-secondary/20">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-20 right-20 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-secondary-accent/10 rounded-full blur-3xl"></div>

      <div className="container-custom relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px] py-20">
          {/* Content */}
          <div className="space-y-8 animate-fade-in">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium">
              <Star className="w-4 h-4 mr-2 fill-current" />
              Test AI più accurato d&apos;Italia
            </div>

            {/* Main heading */}
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="text-gradient">Scopri se hai</span>
                <br />
                <span className="text-foreground">la scabbia in</span>
                <br />
                <span className="text-primary">60 secondi</span>
              </h1>

              <p className="text-lg text-muted-foreground max-w-lg leading-relaxed">
                Il nostro test AI avanzato analizza i tuoi sintomi e ti fornisce una valutazione
                immediata con consigli medici personalizzati.
              </p>
            </div>

            {/* Features */}
            <div className="flex flex-wrap gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <feature.icon className="w-4 h-4 text-primary" />
                  <span>{feature.text}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/scabbia-checker">
                <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary shadow-large group">
                  Test AI Solo €1
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Link href="#come-funziona">
                <Button variant="outline" size="lg" className="border-2">
                  Come Funziona
                </Button>
              </Link>
            </div>

            {/* Value Proposition */}
            <div className="bg-gradient-to-r from-success/10 to-primary/10 rounded-lg p-4 border border-success/20">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-success mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-foreground">
                    Analisi AI avanzata a soli €1 vs €100+ di una visita dermatologica
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Risultati simili a una consulenza specialistica, ma non sostituisce la diagnosi medica professionale
                  </p>
                </div>
              </div>
            </div>

            {/* Trust indicators */}
            <div className="pt-8 border-t border-border/50">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-muted-foreground">
                {trustIndicators.map((indicator, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-success flex-shrink-0" />
                    <span>{indicator}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative lg:block animate-slide-up">
            <div className="relative">
              {/* Placeholder for medical illustration */}
              <div className="relative w-full h-96 lg:h-[500px] bg-gradient-to-br from-primary/5 to-secondary-accent/5 rounded-3xl border border-border/50 flex items-center justify-center overflow-hidden">
                {/* Medical icons floating animation */}
                <div className="absolute inset-0">
                  <div className="absolute top-20 left-20 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center animate-float">
                    <Shield className="w-6 h-6 text-primary" />
                  </div>
                  <div className="absolute top-32 right-16 w-10 h-10 bg-success/10 rounded-full flex items-center justify-center animate-float" style={{animationDelay: '1s'}}>
                    <CheckCircle className="w-5 h-5 text-success" />
                  </div>
                  <div className="absolute bottom-32 left-16 w-14 h-14 bg-secondary-accent/10 rounded-full flex items-center justify-center animate-float" style={{animationDelay: '2s'}}>
                    <Clock className="w-7 h-7 text-secondary-accent" />
                  </div>
                </div>

                {/* Central medical illustration placeholder */}
                <div className="text-center space-y-4">
                  <div className="w-32 h-32 mx-auto bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center shadow-large">
                    <Shield className="w-16 h-16 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">Test Medico AI</h3>
                    <p className="text-sm text-muted-foreground">Tecnologia avanzata per diagnosi accurate</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary rounded-full animate-pulse-soft"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-secondary-accent rounded-full animate-pulse-soft" style={{animationDelay: '1s'}}></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

