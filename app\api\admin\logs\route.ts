import { NextResponse, NextRequest } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { logger, LogLevel } from "@/lib/logger";
import {
  getClientIp,
  getSecureHeaders,
  authRateLimiter
} from "@/lib/security";

export async function GET(request: Request) {
  const secureHeaders = getSecureHeaders();
  const clientIp = getClientIp(request as unknown as NextRequest);

  // Rate limiting
  if (!authRateLimiter.isAllowed(clientIp)) {
    return NextResponse.json(
      { error: "Rate limit exceeded" },
      { status: 429, headers: secureHeaders }
    );
  }

  // Authentication check
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.json(
      { error: "Unauthorized" }, 
      { status: 401, headers: secureHeaders }
    );
  }

  try {
    const url = new URL(request.url);
    const level = url.searchParams.get('level') as LogLevel || LogLevel.SECURITY;
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const stats = url.searchParams.get('stats') === 'true';

    // Get logs
    const logs = logger.getRecentLogs(level, limit);
    
    // Get statistics if requested
    let statistics = null;
    if (stats) {
      statistics = logger.getLogStats(level, 24);
    }

    return NextResponse.json({
      logs,
      statistics,
      level,
      total: logs.length
    }, { headers: secureHeaders });

  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: "Failed to fetch logs" },
      { status: 500, headers: secureHeaders }
    );
  }
}

export async function DELETE(request: Request) {
  const secureHeaders = getSecureHeaders();
  const clientIp = getClientIp(request as unknown as NextRequest);

  // Rate limiting
  if (!authRateLimiter.isAllowed(clientIp)) {
    return NextResponse.json(
      { error: "Rate limit exceeded" },
      { status: 429, headers: secureHeaders }
    );
  }

  // Authentication check
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.json(
      { error: "Unauthorized" }, 
      { status: 401, headers: secureHeaders }
    );
  }

  try {
    const url = new URL(request.url);
    const days = parseInt(url.searchParams.get('days') || '30');

    // Clear old logs
    logger.clearOldLogs(days);

    // Log this action
    logger.info('Admin', 'Log cleanup performed', {
      userId: user.id,
      ip: clientIp,
      details: { daysToKeep: days }
    });

    return NextResponse.json({
      success: true,
      message: `Logs older than ${days} days have been cleared`
    }, { headers: secureHeaders });

  } catch (error) {
    console.error('Error clearing logs:', error);
    return NextResponse.json(
      { error: "Failed to clear logs" },
      { status: 500, headers: secureHeaders }
    );
  }
}
