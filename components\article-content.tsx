"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, HelpCircle, CheckCircle, Co<PERSON>, Check } from "lucide-react";
import { toast } from "react-hot-toast";

interface ArticleContentProps {
  content: string;
  onHeadingsExtracted?: (headings: Array<{ id: string; text: string; level: number }>) => void;
}

interface FAQItem {
  question: string;
  answer: string;
}

export default function ArticleContent({ content, onHeadingsExtracted }: ArticleContentProps) {
  const [processedContent, setProcessedContent] = useState("");
  const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
  const [openFAQs, setOpenFAQs] = useState<Set<number>>(new Set());
  const [copiedBlocks, setCopiedBlocks] = useState<Set<string>>(new Set());
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const processContent = () => {
      let processed = content;
      const headings: Array<{ id: string; text: string; level: number }> = [];
      const faqs: FAQItem[] = [];

      // Extract and fix FAQ sections
      const faqRegex = /details\s*summary([^\/]*?)\/summary\s*([^\/]*?)\/details/g;
      let faqMatch;
      let faqIndex = 0;

      while ((faqMatch = faqRegex.exec(content)) !== null) {
        const question = faqMatch[1].trim();
        const answer = faqMatch[2].trim();
        
        if (question && answer) {
          faqs.push({ question, answer });
          // Replace the malformed details with a placeholder
          processed = processed.replace(faqMatch[0], `<div class="faq-placeholder" data-faq-index="${faqIndex}"></div>`);
          faqIndex++;
        }
      }

      // Extract headings for table of contents
      const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/g;
      let headingMatch;
      let headingIndex = 0;

      while ((headingMatch = headingRegex.exec(processed)) !== null) {
        const level = parseInt(headingMatch[1]);
        const text = headingMatch[2].replace(/<[^>]*>/g, '').trim();
        const id = `heading-${headingIndex}`;
        
        headings.push({ id, text, level });
        
        // Add ID to heading for navigation
        processed = processed.replace(
          headingMatch[0],
          `<h${level} id="${id}" class="scroll-mt-20">${headingMatch[2]}</h${level}>`
        );
        headingIndex++;
      }

      // Improve table styling
      processed = processed.replace(
        /<table>/g,
        '<div class="table-container"><table class="enhanced-table">'
      );
      processed = processed.replace(
        /<\/table>/g,
        '</table></div>'
      );

      // Handle script tags and code blocks with unique IDs for copy functionality
      let blockIndex = 0;

      processed = processed.replace(
        /<script([^>]*)>([\s\S]*?)<\/script>/g,
        (match, attrs, content) => {
          const id = `script-block-${blockIndex++}`;
          return `<div class="script-block-container relative" data-block-id="${id}">
            <div class="script-block-header">
              <span class="script-block-label">Script</span>
              <span class="script-block-type">JSON-LD</span>
              <button class="copy-button" data-copy-target="${id}" title="Copia codice">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>
            <div class="script-block-wrapper">
              <pre class="script-block" id="${id}"><code>${content}</code></pre>
            </div>
          </div>`;
        }
      );

      // Enhance pre/code blocks (but skip already processed script blocks)
      processed = processed.replace(
        /<pre><code class="language-([^"]*)">([\s\S]*?)<\/code><\/pre>/g,
        (match, lang, content) => {
          // Skip if this is already inside a script-block-container
          if (processed.includes(`<pre class="script-block"`)) {
            return match;
          }
          const id = `code-block-${blockIndex++}`;
          return `<div class="code-block-container relative" data-block-id="${id}">
            <div class="code-block-header">
              <span class="code-block-label">Code</span>
              <span class="code-block-language">${lang}</span>
              <button class="copy-button" data-copy-target="${id}" title="Copia codice">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>
            <div class="code-block-wrapper">
              <pre class="code-block" id="${id}"><code class="language-${lang}">${content}</code></pre>
            </div>
          </div>`;
        }
      );

      // Handle remaining pre blocks (but exclude script blocks and already processed ones)
      processed = processed.replace(
        /<pre(?![^>]*class="(?:script-block|code-block)")([^>]*)>([\s\S]*?)<\/pre>/g,
        (match, attrs, content) => {
          // Skip if this is inside a container already
          if (content.includes('script-block-container') || content.includes('code-block-container')) {
            return match;
          }
          const id = `code-block-${blockIndex++}`;
          return `<div class="code-block-container relative" data-block-id="${id}">
            <div class="code-block-header">
              <span class="code-block-label">Code</span>
              <button class="copy-button" data-copy-target="${id}" title="Copia codice">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>
            <div class="code-block-wrapper">
              <pre class="code-block" id="${id}"${attrs}>${content}</pre>
            </div>
          </div>`;
        }
      );

      // Add styling classes to various elements
      processed = processed.replace(/<ul>/g, '<ul class="enhanced-list">');
      processed = processed.replace(/<ol>/g, '<ol class="enhanced-list numbered">');
      processed = processed.replace(/<blockquote>/g, '<blockquote class="enhanced-quote">');
      processed = processed.replace(/<hr>/g, '<hr class="enhanced-divider">');

      // Clean up any remaining malformed elements
      processed = processed.replace(/details\s*summary/g, '');
      processed = processed.replace(/\/summary/g, '');
      processed = processed.replace(/\/details/g, '');

      setProcessedContent(processed);
      setFaqItems(faqs);
      onHeadingsExtracted?.(headings);
    };

    processContent();
  }, [content, onHeadingsExtracted]);

  const toggleFAQ = (index: number) => {
    const newOpenFAQs = new Set(openFAQs);
    if (newOpenFAQs.has(index)) {
      newOpenFAQs.delete(index);
    } else {
      newOpenFAQs.add(index);
    }
    setOpenFAQs(newOpenFAQs);
  };

  const handleCopyCode = async (blockId: string) => {
    try {
      const element = document.getElementById(blockId);
      if (element) {
        const text = element.textContent || '';
        await navigator.clipboard.writeText(text);
        setCopiedBlocks(prev => new Set([...prev, blockId]));
        toast.success('Codice copiato negli appunti!');

        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopiedBlocks(prev => {
            const newSet = new Set(prev);
            newSet.delete(blockId);
            return newSet;
          });
        }, 2000);
      }
    } catch (error) {
      toast.error('Errore nel copiare il codice');
    }
  };

  // Add event listeners for copy buttons after content is rendered
  useEffect(() => {
    if (!contentRef.current) return;

    const copyButtons = contentRef.current.querySelectorAll('.copy-button');

    const handleCopyClick = (event: Event) => {
      const button = event.currentTarget as HTMLButtonElement;
      const targetId = button.getAttribute('data-copy-target');
      if (targetId) {
        handleCopyCode(targetId);
      }
    };

    copyButtons.forEach(button => {
      button.addEventListener('click', handleCopyClick);
    });

    return () => {
      copyButtons.forEach(button => {
        button.removeEventListener('click', handleCopyClick);
      });
    };
  }, [processedContent]);

  const renderFAQSection = () => {
    if (faqItems.length === 0) return null;

    return (
      <div className="faq-section mt-16 mb-12">
        <div className="text-center mb-12">
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-large mx-auto mb-6">
            <HelpCircle className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-foreground mb-4">Domande Frequenti</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Trova risposte immediate alle domande più comuni sulla scabbia
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary-accent rounded-full mx-auto mt-6"></div>
        </div>

        <div className="space-y-6 max-w-4xl mx-auto">
          {faqItems.map((faq, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:shadow-large transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-0">
                <Button
                  variant="ghost"
                  className="w-full p-8 h-auto justify-between text-left hover:bg-muted/50 transition-all duration-200"
                  onClick={() => toggleFAQ(index)}
                >
                  <div className="flex items-start gap-6 flex-1">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center flex-shrink-0 shadow-medium">
                      <span className="text-white font-bold text-sm">{index + 1}</span>
                    </div>
                    <span className="font-semibold text-foreground text-left leading-relaxed text-lg">
                      {faq.question}
                    </span>
                  </div>
                  <div className={`transition-transform duration-200 ${openFAQs.has(index) ? 'rotate-180' : ''}`}>
                    <ChevronDown className="w-6 h-6 text-muted-foreground flex-shrink-0" />
                  </div>
                </Button>

                {openFAQs.has(index) && (
                  <div className="px-8 pb-8 pt-0 animate-slide-up">
                    <div className="ml-16 p-6 bg-gradient-to-r from-muted/50 to-primary/5 rounded-xl border-l-4 border-primary shadow-medium">
                      <div className="flex items-start gap-4">
                        <CheckCircle className="w-6 h-6 text-success flex-shrink-0 mt-1" />
                        <div
                          className="text-muted-foreground leading-relaxed text-base enhanced-prose"
                          dangerouslySetInnerHTML={{ __html: faq.answer }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderProcessedContent = () => {
    // Split content by FAQ placeholders
    const parts = processedContent.split(/<div class="faq-placeholder" data-faq-index="\d+"><\/div>/);
    const faqPlaceholders = processedContent.match(/<div class="faq-placeholder" data-faq-index="(\d+)"><\/div>/g) || [];
    
    return (
      <div className="article-content z-content" ref={contentRef}>
        {parts.map((part, index) => (
          <div key={index}>
            {part && (
              <div
                className="prose prose-lg max-w-none enhanced-prose"
                dangerouslySetInnerHTML={{ __html: part }}
              />
            )}
            {index < faqPlaceholders.length && index === parts.length - 2 && renderFAQSection()}
          </div>
        ))}
        {faqItems.length > 0 && parts.length === 1 && renderFAQSection()}
      </div>
    );
  };

  return renderProcessedContent();
}
