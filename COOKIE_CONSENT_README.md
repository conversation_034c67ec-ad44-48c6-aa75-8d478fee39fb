# Cookie Consent System - ScabbiaSintomi.it

## Overview

This implementation provides a comprehensive, GDPR-compliant cookie consent system for the ScabbiaSintomi.it website, specifically designed to meet Italian privacy law requirements.

## Features

### ✅ GDPR Compliance
- Follows Italian Data Protection Authority (Garante) guidelines
- Granular consent for different cookie categories
- Clear information about cookie usage
- Easy withdrawal of consent
- Proper documentation and audit trail

### ✅ User Experience
- Professional, non-intrusive banner design
- Mobile-responsive interface
- Matches existing website aesthetic
- Italian language throughout
- Accessible with keyboard navigation

### ✅ Technical Implementation
- TypeScript for type safety
- React hooks for easy integration
- Local storage for persistence
- Event system for real-time updates
- Version control for policy updates

## Cookie Categories

1. **<PERSON>ie <PERSON>i** (Essential)
   - Always enabled (cannot be disabled)
   - Required for basic website functionality
   - Session management, security, core features

2. **<PERSON>ie Analitici** (Analytics)
   - Optional
   - Website usage statistics
   - Performance monitoring
   - User behavior analysis

3. **<PERSON><PERSON> di Preferenze** (Preferences)
   - Optional
   - User interface preferences
   - Language settings
   - Theme preferences

4. **<PERSON><PERSON> di Marketing** (Marketing)
   - Optional
   - Advertising and retargeting
   - Social media integration
   - Promotional content

## Usage

### Basic Hook Usage

```typescript
import { useCookieConsent } from '@/hooks/use-cookie-consent';

function MyComponent() {
  const { hasConsentFor, hasConsented, isLoading } = useCookieConsent();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      {hasConsentFor('analytics') && (
        <AnalyticsComponent />
      )}
      {hasConsentFor('marketing') && (
        <MarketingComponent />
      )}
    </div>
  );
}
```

### Utility Functions

```typescript
import { 
  hasConsentFor, 
  hasGivenConsent, 
  saveCookieConsent,
  acceptAllCookies,
  acceptEssentialOnly,
  clearCookieConsent 
} from '@/lib/cookie-consent';

// Check specific consent
if (hasConsentFor('analytics')) {
  // Load analytics
}

// Check if any consent given
if (hasGivenConsent()) {
  // User has made a choice
}

// Programmatically set consent
saveCookieConsent({
  essential: true,
  analytics: true,
  preferences: false,
  marketing: false
});
```

### Event Listening

```typescript
useEffect(() => {
  const handleConsentChange = (event: CustomEvent) => {
    const consentState = event.detail;
    console.log('Consent changed:', consentState);
  };

  window.addEventListener('cookieConsentChanged', handleConsentChange);
  
  return () => {
    window.removeEventListener('cookieConsentChanged', handleConsentChange);
  };
}, []);
```

## Components

### CookieBanner
- Main consent banner shown to new users
- Appears at bottom of page after 1 second delay
- Provides quick accept/decline options
- Links to detailed preferences

### CookiePreferencesManager
- Detailed cookie category management
- Available on `/cookie` page
- Granular control over each category
- Real-time status display

## Integration Points

### Layout Integration
The cookie banner is automatically included in the root layout (`app/layout.tsx`).

### Page Integration
The cookie preferences manager is available on the cookie policy page (`/cookie`).

### Analytics Integration
Use the `AnalyticsWrapper` component or `useCookieConsent` hook to conditionally load analytics based on user consent.

## Legal Compliance

### Italian GDPR Requirements
- ✅ Clear information about cookie usage
- ✅ Granular consent options
- ✅ Easy consent withdrawal
- ✅ No pre-ticked boxes for non-essential cookies
- ✅ Proper documentation
- ✅ Links to privacy policy

### Data Storage
- Consent preferences stored in browser localStorage
- No server-side tracking of consent choices
- Versioned consent to handle policy updates
- Automatic cleanup of outdated consent data

## Maintenance

### Adding New Cookie Categories
1. Update `CookieConsent` interface in `types/cookie-consent.ts`
2. Add category to `getCookieCategories()` in `lib/cookie-consent.ts`
3. Update default consent in `defaultCookieConsent`

### Updating Cookie Policy
1. Increment `COOKIE_CONSENT_VERSION` in `types/cookie-consent.ts`
2. Update policy text in `app/cookie/page.tsx`
3. Users will be prompted for new consent on next visit

### Testing
- Test banner appearance on first visit
- Test preference persistence across sessions
- Test all consent combinations
- Verify analytics loading based on consent
- Test mobile responsiveness

## Browser Support
- Modern browsers with localStorage support
- Graceful degradation for older browsers
- No JavaScript fallback shows essential cookies only

## Performance
- Minimal bundle size impact
- Lazy loading of preference components
- Efficient localStorage operations
- No external dependencies for core functionality
