"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Home, 
  Search, 
  Stethoscope, 
  ArrowLeft, 
  Bug,
  Heart,
  Sparkles 
} from "lucide-react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";

export default function NotFound() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container-custom py-20">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center space-y-8"
            >
              {/* Animated Medical Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="relative mx-auto w-32 h-32"
              >
                <motion.div
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center"
                >
                  <Bug className="w-16 h-16 text-primary" />
                </motion.div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute -top-2 -right-2"
                >
                  <Sparkles className="w-8 h-8 text-secondary-accent" />
                </motion.div>
              </motion.div>

              {/* Error Message */}
              <div className="space-y-4">
                <Badge variant="outline" className="mb-4">
                  Errore 404
                </Badge>
                <h1 className="text-4xl sm:text-6xl font-bold text-foreground">
                  Oops! Questa pagina ha sviluppato{" "}
                  <span className="text-gradient">sintomi strani</span>
                </h1>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                  Sembra che questa pagina sia scomparsa più velocemente di un acaro della scabbia! 
                  Non preoccuparti, la nostra AI medica ti aiuterà a trovare quello che cerchi.
                </p>
              </div>

              {/* Action Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.6 }}
                className="grid md:grid-cols-3 gap-6 mt-12"
              >
                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader className="text-center pb-4">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-3"
                    >
                      <Home className="w-6 h-6 text-primary" />
                    </motion.div>
                    <CardTitle className="text-lg">Torna alla Home</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground mb-4 text-sm">
                      Ricomincia dalla pagina principale
                    </p>
                    <Link href="/">
                      <Button className="w-full bg-gradient-to-r from-primary to-primary-dark">
                        Vai alla Home
                        <Home className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader className="text-center pb-4">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="mx-auto w-12 h-12 bg-secondary-accent/10 rounded-full flex items-center justify-center mb-3"
                    >
                      <Stethoscope className="w-6 h-6 text-secondary-accent" />
                    </motion.div>
                    <CardTitle className="text-lg">Test AI Scabbia</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground mb-4 text-sm">
                      Fai il nostro test AI per €1
                    </p>
                    <Link href="/scabbia-checker">
                      <Button variant="outline" className="w-full">
                        Inizia il Test
                        <Stethoscope className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader className="text-center pb-4">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="mx-auto w-12 h-12 bg-success/10 rounded-full flex items-center justify-center mb-3"
                    >
                      <Search className="w-6 h-6 text-success" />
                    </motion.div>
                    <CardTitle className="text-lg">Esplora Contenuti</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground mb-4 text-sm">
                      Scopri sintomi, cause e cure
                    </p>
                    <Link href="/sintomi">
                      <Button variant="outline" className="w-full">
                        Scopri di Più
                        <Search className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Fun Medical Fact */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.6 }}
                className="mt-12"
              >
                <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
                  <CardContent className="p-6 text-center">
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Heart className="w-5 h-5 text-primary" />
                      <span className="font-semibold text-primary">Lo sapevi?</span>
                    </div>
                    <p className="text-muted-foreground">
                      La scabbia è una delle condizioni dermatologiche più antiche conosciute dall'uomo, 
                      ma oggi può essere diagnosticata rapidamente con l'aiuto dell'intelligenza artificiale!
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Back Button */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.6 }}
                className="pt-8"
              >
                <Button 
                  variant="ghost" 
                  onClick={() => window.history.back()}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Torna Indietro
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
