'use client';

import { useState } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-hot-toast';

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    privacy: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      privacy: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name || !formData.email || !formData.message) {
      toast.error('Per favore compila tutti i campi obbligatori.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('Inserisci un indirizzo email valido.');
      return;
    }

    if (!formData.privacy) {
      toast.error('Devi accettare la privacy policy per continuare.');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 429) {
          toast.error(`Troppe richieste. Riprova tra ${result.retryAfter || 60} secondi.`);
        } else if (result.details && Array.isArray(result.details)) {
          // Show validation errors
          result.details.forEach((error: string) => {
            toast.error(error);
          });
        } else {
          toast.error(result.error || 'Si è verificato un errore durante l\'invio del messaggio.');
        }
        return;
      }

      // Success
      toast.success('Grazie per il tuo messaggio! Ti risponderemo il prima possibile.');

      // Show additional info if confirmation email wasn't sent
      if (result.details && !result.details.confirmationSent) {
        toast('Nota: Non siamo riusciti a inviarti l\'email di conferma, ma abbiamo ricevuto il tuo messaggio.', {
          icon: '⚠️',
          duration: 5000,
        });
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        privacy: false
      });

    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Si è verificato un errore durante l\'invio del messaggio. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-large border-0 bg-card/50 backdrop-blur-sm">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-foreground">
          Contattaci
        </CardTitle>
        <p className="text-muted-foreground">
          Hai domande o dubbi? Siamo qui per aiutarti. Compila il form e ti risponderemo il prima possibile.
        </p>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              Nome completo <span className="text-destructive">*</span>
            </Label>
            <Input
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="Inserisci il tuo nome"
              className="h-11"
              required
            />
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              Indirizzo email <span className="text-destructive">*</span>
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              className="h-11"
              required
            />
          </div>

          {/* Subject Field */}
          <div className="space-y-2">
            <Label htmlFor="subject" className="text-sm font-medium">
              Oggetto
            </Label>
            <select
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              className="flex h-11 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="">Seleziona un argomento</option>
              <option value="info">Informazioni generali</option>
              <option value="support">Supporto tecnico</option>
              <option value="medical">Domande mediche</option>
              <option value="feedback">Feedback</option>
              <option value="other">Altro</option>
            </select>
          </div>

          {/* Message Field */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium">
              Messaggio <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              placeholder="Scrivi qui il tuo messaggio..."
              className="min-h-[120px] resize-none"
              required
            />
          </div>

          {/* Privacy Checkbox */}
          <div className="flex items-start space-x-3">
            <Checkbox
              id="privacy"
              checked={formData.privacy}
              onCheckedChange={handleCheckboxChange}
              className="mt-1"
              required
            />
            <div className="space-y-1 leading-none">
              <Label
                htmlFor="privacy"
                className="text-sm font-medium leading-relaxed cursor-pointer"
              >
                Acconsento al trattamento dei miei dati personali secondo la{' '}
                <a
                  href="/privacy"
                  className="text-primary hover:text-primary-dark underline underline-offset-2"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Privacy Policy
                </a>
                {' '}<span className="text-destructive">*</span>
              </Label>
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full h-12 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary shadow-medium text-base font-medium"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Invio in corso...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Invia messaggio
                </>
              )}
            </Button>
          </div>

          {/* Help text */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              I campi contrassegnati con <span className="text-destructive">*</span> sono obbligatori.
              <br />
              Ti risponderemo entro 24 ore lavorative.
            </p>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
