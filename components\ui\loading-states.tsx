"use client";

import { motion } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Brain, 
  Stethoscope, 
  Heart, 
  Activity, 
  Microscope,
  Sparkles,
  Loader2,
  Search
} from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  message?: string;
  submessage?: string;
  progress?: number;
  variant?: 'default' | 'medical' | 'analysis' | 'payment' | 'minimal';
  className?: string;
}

const medicalMessages = [
  "La nostra AI sta analizzando i tuoi sintomi...",
  "Consultando il database medico...",
  "Elaborando la diagnosi preliminare...",
  "Verificando i pattern sintomatologici...",
  "Preparando il report dettagliato..."
];

const analysisMessages = [
  "Analizzando le tue risposte...",
  "Confrontando con casi simili...",
  "Calcolando la probabilità...",
  "Generando raccomandazioni...",
  "Finalizzando il risultato..."
];

export function LoadingState({
  message,
  submessage,
  progress,
  variant = 'default',
  className
}: LoadingStateProps) {
  const getRandomMessage = () => {
    if (variant === 'medical') {
      return medicalMessages[Math.floor(Math.random() * medicalMessages.length)];
    }
    if (variant === 'analysis') {
      return analysisMessages[Math.floor(Math.random() * analysisMessages.length)];
    }
    return "Caricamento in corso...";
  };

  const displayMessage = message || getRandomMessage();

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center justify-center gap-2", className)}>
        <Loader2 className="w-4 h-4 animate-spin text-primary" />
        <span className="text-sm text-muted-foreground">{displayMessage}</span>
      </div>
    );
  }

  return (
    <div className={cn(
      "flex items-center justify-center min-h-[400px] p-6",
      className
    )}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-6 max-w-md mx-auto"
      >
        {/* Animated Icon */}
        <LoadingIcon variant={variant} />

        {/* Message */}
        <div className="space-y-2">
          <motion.h3
            key={displayMessage}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-lg font-semibold text-foreground"
          >
            {displayMessage}
          </motion.h3>
          {submessage && (
            <p className="text-sm text-muted-foreground">
              {submessage}
            </p>
          )}
        </div>

        {/* Progress Bar */}
        {progress !== undefined && (
          <div className="w-full space-y-2">
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {progress}% completato
            </p>
          </div>
        )}

        {/* Floating Elements */}
        <FloatingElements variant={variant} />
      </motion.div>
    </div>
  );
}

function LoadingIcon({ variant }: { variant: string }) {
  const iconProps = {
    className: "w-12 h-12 text-primary",
    strokeWidth: 1.5
  };

  const getIcon = () => {
    switch (variant) {
      case 'medical':
        return <Stethoscope {...iconProps} />;
      case 'analysis':
        return <Brain {...iconProps} />;
      case 'payment':
        return <Heart {...iconProps} />;
      default:
        return <Activity {...iconProps} />;
    }
  };

  return (
    <motion.div
      animate={{
        scale: [1, 1.1, 1],
        rotate: variant === 'analysis' ? [0, 180, 360] : [0, 10, -10, 0]
      }}
      transition={{
        duration: variant === 'analysis' ? 2 : 3,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      className="mx-auto w-16 h-16 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center"
    >
      {getIcon()}
    </motion.div>
  );
}

function FloatingElements({ variant }: { variant: string }) {
  const elements = [
    { icon: Sparkles, delay: 0, x: -20, y: -10 },
    { icon: Heart, delay: 0.5, x: 20, y: -15 },
    { icon: Microscope, delay: 1, x: -15, y: 10 },
    { icon: Search, delay: 1.5, x: 15, y: 15 }
  ];

  if (variant === 'minimal') return null;

  return (
    <div className="absolute inset-0 pointer-events-none">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
            x: [0, element.x, 0],
            y: [0, element.y, 0]
          }}
          transition={{
            duration: 3,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <element.icon className="w-4 h-4 text-secondary-accent/60" />
        </motion.div>
      ))}
    </div>
  );
}

// Medical-themed loader specifically for scabies test
export function MedicalLoader({
  stage = 'analyzing',
  progress,
  className
}: {
  stage?: 'analyzing' | 'processing' | 'generating' | 'finalizing';
  progress?: number;
  className?: string;
}) {
  const stageMessages = {
    analyzing: "Analizzando i sintomi della scabbia...",
    processing: "Elaborando i dati clinici...",
    generating: "Generando il report diagnostico...",
    finalizing: "Finalizzando la valutazione..."
  };

  const stageIcons = {
    analyzing: Microscope,
    processing: Brain,
    generating: Activity,
    finalizing: Stethoscope
  };

  const IconComponent = stageIcons[stage];

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center",
      className
    )}>
      <Card className="w-full max-w-md mx-4 border-primary/20 bg-gradient-to-br from-white to-primary/5">
        <CardContent className="p-8 text-center space-y-6">
          {/* Animated Medical Icon */}
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{
              rotate: { duration: 3, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
            className="mx-auto w-20 h-20 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center"
          >
            <IconComponent className="w-10 h-10 text-primary" />
          </motion.div>

          {/* Stage Message */}
          <div className="space-y-2">
            <h3 className="text-xl font-bold text-foreground">
              Analisi AI in Corso
            </h3>
            <motion.p
              key={stage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-muted-foreground"
            >
              {stageMessages[stage]}
            </motion.p>
          </div>

          {/* Progress */}
          {progress !== undefined && (
            <div className="space-y-3">
              <Progress value={progress} className="h-3" />
              <p className="text-sm text-muted-foreground">
                {progress}% completato
              </p>
            </div>
          )}

          {/* Medical Fact */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.5 }}
            className="bg-primary/5 rounded-lg p-4 border border-primary/10"
          >
            <div className="flex items-center gap-2 mb-2">
              <Heart className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-primary">Lo sapevi?</span>
            </div>
            <p className="text-xs text-muted-foreground">
              L'AI può analizzare i sintomi della scabbia con un'accuratezza del 95%, 
              aiutandoti a ottenere una valutazione preliminare rapida e affidabile.
            </p>
          </motion.div>

          {/* Pulsing Dots */}
          <div className="flex justify-center gap-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  delay: i * 0.2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="w-2 h-2 bg-primary rounded-full"
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Simple skeleton loader for content
export function SkeletonLoader({ 
  lines = 3, 
  className 
}: { 
  lines?: number; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: i * 0.1 }}
          className="h-4 bg-muted rounded animate-pulse"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
  );
}
