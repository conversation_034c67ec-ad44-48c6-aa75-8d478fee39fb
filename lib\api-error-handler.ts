/**
 * Centralized API Error Handling Utilities
 * Provides friendly, medical-themed error messages for the scabies symptom website
 */

export interface ApiError {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high';
  retryable: boolean;
  retryAfter?: number;
}

export interface ApiErrorResponse {
  error: string;
  code?: string;
  retryAfter?: number;
  details?: any;
}

/**
 * Maps technical error codes to user-friendly messages
 */
const ERROR_MESSAGES: Record<string, Omit<ApiError, 'code'>> = {
  // Network & Connection Errors
  'NETWORK_ERROR': {
    message: 'Network connection failed',
    userMessage: 'La nostra AI medica ha perso la connessione. Verifica la tua connessione internet e riprova.',
    severity: 'medium',
    retryable: true
  },
  'TIMEOUT_ERROR': {
    message: 'Request timeout',
    userMessage: 'Il nostro dottore AI sta impiegando più tempo del solito. Riprova tra un momento!',
    severity: 'medium',
    retryable: true,
    retryAfter: 30
  },
  'CONNECTION_REFUSED': {
    message: 'Connection refused',
    userMessage: 'I nostri server sono temporaneamente non disponibili. Riprova tra qualche minuto.',
    severity: 'high',
    retryable: true,
    retryAfter: 300
  },

  // Rate Limiting
  'RATE_LIMIT_EXCEEDED': {
    message: 'Rate limit exceeded',
    userMessage: 'Hai fatto troppe richieste. Prenditi una pausa e riprova tra qualche minuto!',
    severity: 'low',
    retryable: true,
    retryAfter: 60
  },

  // Authentication & Authorization
  'UNAUTHORIZED': {
    message: 'Unauthorized access',
    userMessage: 'Sessione scaduta. Per favore, effettua nuovamente l\'accesso.',
    severity: 'medium',
    retryable: false
  },
  'FORBIDDEN': {
    message: 'Access forbidden',
    userMessage: 'Non hai i permessi necessari per questa operazione.',
    severity: 'medium',
    retryable: false
  },

  // Payment Errors
  'PAYMENT_FAILED': {
    message: 'Payment processing failed',
    userMessage: 'C\'è stato un problema con il pagamento. Verifica i dati della carta e riprova.',
    severity: 'medium',
    retryable: true
  },
  'PAYMENT_DECLINED': {
    message: 'Payment declined',
    userMessage: 'Il pagamento è stato rifiutato. Controlla i dati della carta o prova con un altro metodo.',
    severity: 'medium',
    retryable: true
  },
  'INSUFFICIENT_FUNDS': {
    message: 'Insufficient funds',
    userMessage: 'Fondi insufficienti. Verifica il saldo della carta o usa un altro metodo di pagamento.',
    severity: 'medium',
    retryable: true
  },

  // AI/OpenAI Specific Errors
  'AI_QUOTA_EXCEEDED': {
    message: 'AI API quota exceeded',
    userMessage: 'La nostra AI ha raggiunto il limite giornaliero. Riprova domani o contatta il supporto.',
    severity: 'high',
    retryable: false
  },
  'AI_INVALID_REQUEST': {
    message: 'Invalid AI request',
    userMessage: 'C\'è stato un problema nell\'elaborazione della tua richiesta. Riprova con informazioni diverse.',
    severity: 'medium',
    retryable: true
  },
  'AI_CONTEXT_TOO_LONG': {
    message: 'AI context length exceeded',
    userMessage: 'Le informazioni fornite sono troppo dettagliate. Prova a essere più conciso.',
    severity: 'low',
    retryable: true
  },
  'AI_SERVICE_UNAVAILABLE': {
    message: 'AI service unavailable',
    userMessage: 'Il nostro dottore AI è temporaneamente non disponibile. Riprova tra qualche minuto.',
    severity: 'high',
    retryable: true,
    retryAfter: 180
  },

  // Server Errors
  'INTERNAL_SERVER_ERROR': {
    message: 'Internal server error',
    userMessage: 'Si è verificato un errore interno. Il nostro team tecnico è stato notificato.',
    severity: 'high',
    retryable: true,
    retryAfter: 60
  },
  'SERVICE_UNAVAILABLE': {
    message: 'Service unavailable',
    userMessage: 'Il servizio è temporaneamente non disponibile per manutenzione. Riprova più tardi.',
    severity: 'high',
    retryable: true,
    retryAfter: 600
  },
  'BAD_GATEWAY': {
    message: 'Bad gateway',
    userMessage: 'Problemi di comunicazione con i nostri server. Riprova tra qualche minuto.',
    severity: 'high',
    retryable: true,
    retryAfter: 120
  },

  // Validation Errors
  'VALIDATION_ERROR': {
    message: 'Validation failed',
    userMessage: 'Alcuni dati inseriti non sono validi. Controlla e riprova.',
    severity: 'low',
    retryable: true
  },
  'MISSING_REQUIRED_FIELD': {
    message: 'Missing required field',
    userMessage: 'Mancano alcune informazioni obbligatorie. Completa tutti i campi richiesti.',
    severity: 'low',
    retryable: true
  },

  // Default fallback
  'UNKNOWN_ERROR': {
    message: 'Unknown error occurred',
    userMessage: 'Si è verificato un errore imprevisto. Se il problema persiste, contatta il supporto.',
    severity: 'medium',
    retryable: true
  }
};

/**
 * Processes API error responses and returns user-friendly error information
 */
export function processApiError(error: any): ApiError {
  let errorCode = 'UNKNOWN_ERROR';
  let originalMessage = 'Unknown error';

  // Handle different error types
  if (error instanceof Error) {
    originalMessage = error.message;
    
    // Map common error patterns to codes
    if (error.message.includes('fetch')) {
      errorCode = 'NETWORK_ERROR';
    } else if (error.message.includes('timeout')) {
      errorCode = 'TIMEOUT_ERROR';
    } else if (error.message.includes('quota')) {
      errorCode = 'AI_QUOTA_EXCEEDED';
    } else if (error.message.includes('context_length')) {
      errorCode = 'AI_CONTEXT_TOO_LONG';
    }
  } else if (typeof error === 'object' && error !== null) {
    // Handle API response errors
    if (error.code) {
      errorCode = error.code;
    } else if (error.status) {
      // Map HTTP status codes
      switch (error.status) {
        case 401:
          errorCode = 'UNAUTHORIZED';
          break;
        case 403:
          errorCode = 'FORBIDDEN';
          break;
        case 429:
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorCode = 'BAD_GATEWAY';
          break;
        case 503:
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        case 504:
          errorCode = 'TIMEOUT_ERROR';
          break;
      }
    }
    
    if (error.message) {
      originalMessage = error.message;
    }
  }

  // Get error details or use default
  const errorDetails = ERROR_MESSAGES[errorCode] || ERROR_MESSAGES['UNKNOWN_ERROR'];

  return {
    code: errorCode,
    message: originalMessage,
    ...errorDetails
  };
}

/**
 * Formats error for display in toast notifications
 */
export function formatErrorForToast(error: ApiError): string {
  return error.userMessage;
}

/**
 * Determines if an error should trigger a retry mechanism
 */
export function shouldRetry(error: ApiError, attemptCount: number = 0): boolean {
  const maxRetries = 3;
  return error.retryable && attemptCount < maxRetries;
}

/**
 * Gets the delay before next retry attempt
 */
export function getRetryDelay(error: ApiError, attemptCount: number = 0): number {
  if (error.retryAfter) {
    return error.retryAfter * 1000; // Convert to milliseconds
  }
  
  // Exponential backoff: 1s, 2s, 4s
  return Math.min(1000 * Math.pow(2, attemptCount), 10000);
}

/**
 * Logs error for monitoring and debugging
 */
export function logError(error: ApiError, context?: any): void {
  const logData = {
    timestamp: new Date().toISOString(),
    code: error.code,
    message: error.message,
    severity: error.severity,
    context
  };

  if (error.severity === 'high') {
    console.error('High severity API error:', logData);
  } else if (error.severity === 'medium') {
    console.warn('Medium severity API error:', logData);
  } else {
    console.log('Low severity API error:', logData);
  }

  // In production, send to error monitoring service
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrate with error monitoring service (e.g., Sentry)
  }
}
