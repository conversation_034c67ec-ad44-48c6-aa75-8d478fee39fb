"use client";

import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  RefreshCw, 
  X,
  Clock,
  Wifi,
  CreditCard
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ApiError } from "@/lib/api-error-handler";

interface ErrorMessageProps {
  error: ApiError | string;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  variant?: 'inline' | 'toast' | 'card';
  showIcon?: boolean;
  showRetry?: boolean;
  retryLabel?: string;
  retryDisabled?: boolean;
  retryCountdown?: number;
}

const severityConfig = {
  low: {
    icon: Info,
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    textColor: 'text-blue-800 dark:text-blue-200',
    iconColor: 'text-blue-600 dark:text-blue-400'
  },
  medium: {
    icon: AlertCircle,
    bgColor: 'bg-warning-light dark:bg-warning/10',
    borderColor: 'border-warning/30',
    textColor: 'text-warning-foreground dark:text-warning',
    iconColor: 'text-warning'
  },
  high: {
    icon: AlertTriangle,
    bgColor: 'bg-destructive/10',
    borderColor: 'border-destructive/30',
    textColor: 'text-destructive',
    iconColor: 'text-destructive'
  }
};

const getErrorIcon = (error: ApiError | string) => {
  if (typeof error === 'string') return AlertCircle;
  
  switch (error.code) {
    case 'NETWORK_ERROR':
    case 'CONNECTION_REFUSED':
      return Wifi;
    case 'PAYMENT_FAILED':
    case 'PAYMENT_DECLINED':
    case 'INSUFFICIENT_FUNDS':
      return CreditCard;
    case 'TIMEOUT_ERROR':
    case 'RATE_LIMIT_EXCEEDED':
      return Clock;
    default:
      return severityConfig[error.severity]?.icon || AlertCircle;
  }
};

export function ErrorMessage({
  error,
  onRetry,
  onDismiss,
  className,
  variant = 'card',
  showIcon = true,
  showRetry = true,
  retryLabel = 'Riprova',
  retryDisabled = false,
  retryCountdown
}: ErrorMessageProps) {
  const errorObj = typeof error === 'string' 
    ? { code: 'UNKNOWN_ERROR', message: error, userMessage: error, severity: 'medium' as const, retryable: true }
    : error;

  const config = severityConfig[errorObj.severity];
  const IconComponent = showIcon ? getErrorIcon(errorObj) : null;

  const content = (
    <div className={cn(
      "flex items-start gap-3",
      variant === 'inline' && "p-3",
      variant === 'card' && "p-4"
    )}>
      {IconComponent && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200 }}
          className="flex-shrink-0 mt-0.5"
        >
          <IconComponent className={cn("w-5 h-5", config.iconColor)} />
        </motion.div>
      )}
      
      <div className="flex-1 min-w-0">
        <p className={cn("text-sm font-medium", config.textColor)}>
          {errorObj.userMessage}
        </p>
        
        {/* Development mode: show technical details */}
        {process.env.NODE_ENV === 'development' && errorObj.message !== errorObj.userMessage && (
          <p className="text-xs text-muted-foreground mt-1 font-mono">
            {errorObj.code}: {errorObj.message}
          </p>
        )}
      </div>

      <div className="flex items-center gap-2 flex-shrink-0">
        {/* Retry Button */}
        {showRetry && errorObj.retryable && onRetry && (
          <Button
            size="sm"
            variant="outline"
            onClick={onRetry}
            disabled={retryDisabled}
            className={cn(
              "h-8 px-3 text-xs",
              config.textColor,
              config.borderColor
            )}
          >
            {retryCountdown ? (
              <>
                <Clock className="w-3 h-3 mr-1" />
                {retryCountdown}s
              </>
            ) : (
              <>
                <RefreshCw className="w-3 h-3 mr-1" />
                {retryLabel}
              </>
            )}
          </Button>
        )}

        {/* Dismiss Button */}
        {onDismiss && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onDismiss}
            className="h-8 w-8 p-0 hover:bg-transparent"
          >
            <X className="w-4 h-4" />
            <span className="sr-only">Chiudi</span>
          </Button>
        )}
      </div>
    </div>
  );

  if (variant === 'toast') {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "rounded-lg border shadow-lg",
          config.bgColor,
          config.borderColor,
          className
        )}
      >
        {content}
      </motion.div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={cn(
        "rounded-md border",
        config.bgColor,
        config.borderColor,
        className
      )}>
        {content}
      </div>
    );
  }

  // Card variant (default)
  return (
    <Card className={cn(
      config.bgColor,
      config.borderColor,
      className
    )}>
      <CardContent className="p-0">
        {content}
      </CardContent>
    </Card>
  );
}

// Toast-style error notifications
export function ErrorToast({
  errors,
  onDismiss
}: {
  errors: (ApiError | string)[];
  onDismiss: (index: number) => void;
}) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      <AnimatePresence>
        {errors.map((error, index) => (
          <ErrorMessage
            key={index}
            error={error}
            variant="toast"
            onDismiss={() => onDismiss(index)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

// Compact error display for forms
export function FormError({
  error,
  className
}: {
  error: string | null;
  className?: string;
}) {
  if (!error) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.2 }}
      className={cn("text-sm text-destructive flex items-center gap-2 mt-1", className)}
    >
      <AlertCircle className="w-4 h-4 flex-shrink-0" />
      <span>{error}</span>
    </motion.div>
  );
}
