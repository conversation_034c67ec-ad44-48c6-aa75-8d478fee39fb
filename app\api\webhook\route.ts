import { NextResponse, NextRequest } from "next/server";
import <PERSON><PERSON> from "stripe";
import { headers } from 'next/headers';
import {
  getClientIp,
  getSecureHeaders,
  logSecurityEvent,
  strictRateLimiter
} from "@/lib/security";

// Function to get Stripe client (lazy initialization)
function getStripeClient() {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error("STRIPE_SECRET_KEY environment variable is not set");
  }

  return new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: "2025-05-28.basil",
    typescript: true,
  });
}

export async function POST(request: Request) {
  const secureHeaders = getSecureHeaders();
  const clientIp = getClientIp(request as unknown as NextRequest);

  // Strict rate limiting for webhooks
  if (!strictRateLimiter.isAllowed(clientIp)) {
    logSecurityEvent('Webhook rate limit exceeded', { clientIp });
    return NextResponse.json(
      { error: 'Rate limit exceeded' },
      { status: 429, headers: secureHeaders }
    );
  }

  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature') || '';

    // Validate webhook signature
    if (!signature) {
      logSecurityEvent('Webhook missing signature', { clientIp });
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 400, headers: secureHeaders }
      );
    }

    // Validate webhook secret is configured
    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('STRIPE_WEBHOOK_SECRET not configured');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500, headers: secureHeaders }
      );
    }

    let event: Stripe.Event;

    try {
      // Initialize Stripe client only when needed
      const stripe = getStripeClient();
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      logSecurityEvent('Invalid webhook signature', {
        clientIp,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400, headers: secureHeaders }
      );
    }

    // Handle the checkout.session.completed event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      
      // Here you would typically:
      // 1. Verify the payment was successful
      // 2. Update your database to mark the payment as complete
      // 3. Grant access to the purchased service
      
      console.log('Payment successful for session:', session.id);
      
      // You can access the metadata you set earlier
      const service = session.metadata?.service;
      console.log('Service:', service);
      
      // TODO: Update your database or perform other actions
      
      return NextResponse.json({ received: true }, { headers: secureHeaders });
    }

    // Handle other event types as needed
    console.log(`Unhandled event type: ${event.type}`);
    return NextResponse.json({ received: true }, { headers: secureHeaders });
    
  } catch (error) {
    console.error('Webhook error:', error);
    logSecurityEvent('Webhook processing error', {
      clientIp,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500, headers: secureHeaders }
    );
  }
}
