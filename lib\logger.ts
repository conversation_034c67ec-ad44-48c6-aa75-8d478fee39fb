import fs from 'fs';
import path from 'path';

// Log levels
export enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  SECURITY = 'SECURITY'
}

// Log entry interface
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  ip?: string;
  userAgent?: string;
  userId?: string;
  details?: Record<string, unknown>;
}

class Logger {
  private logDir: string;
  private maxLogSize: number = 10 * 1024 * 1024; // 10MB
  private maxLogFiles: number = 5;

  constructor() {
    this.logDir = path.join(process.cwd(), 'logs');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  private getLogFileName(level: LogLevel): string {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${level.toLowerCase()}-${date}.log`);
  }

  private formatLogEntry(entry: LogEntry): string {
    const baseLog = `[${entry.timestamp}] [${entry.level}] [${entry.category}] ${entry.message}`;
    
    const metadata: string[] = [];
    if (entry.ip) metadata.push(`IP: ${entry.ip}`);
    if (entry.userAgent) metadata.push(`UA: ${entry.userAgent}`);
    if (entry.userId) metadata.push(`User: ${entry.userId}`);
    if (entry.details) metadata.push(`Details: ${JSON.stringify(entry.details)}`);
    
    return metadata.length > 0 
      ? `${baseLog} | ${metadata.join(' | ')}`
      : baseLog;
  }

  private writeToFile(level: LogLevel, entry: LogEntry) {
    try {
      const fileName = this.getLogFileName(level);
      const logLine = this.formatLogEntry(entry) + '\n';
      
      // Check file size and rotate if necessary
      if (fs.existsSync(fileName)) {
        const stats = fs.statSync(fileName);
        if (stats.size > this.maxLogSize) {
          this.rotateLogFile(fileName);
        }
      }
      
      fs.appendFileSync(fileName, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private rotateLogFile(fileName: string) {
    try {
      const baseName = fileName.replace('.log', '');
      
      // Remove oldest log file
      const oldestFile = `${baseName}.${this.maxLogFiles}.log`;
      if (fs.existsSync(oldestFile)) {
        fs.unlinkSync(oldestFile);
      }
      
      // Rotate existing files
      for (let i = this.maxLogFiles - 1; i >= 1; i--) {
        const currentFile = `${baseName}.${i}.log`;
        const nextFile = `${baseName}.${i + 1}.log`;
        
        if (fs.existsSync(currentFile)) {
          fs.renameSync(currentFile, nextFile);
        }
      }
      
      // Move current file to .1
      if (fs.existsSync(fileName)) {
        fs.renameSync(fileName, `${baseName}.1.log`);
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private log(level: LogLevel, category: string, message: string, metadata?: Partial<LogEntry>) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      ...metadata
    };

    // Always log to console in development
    if (process.env.NODE_ENV === 'development') {
      const consoleMessage = this.formatLogEntry(entry);
      
      switch (level) {
        case LogLevel.ERROR:
        case LogLevel.SECURITY:
          console.error(consoleMessage);
          break;
        case LogLevel.WARN:
          console.warn(consoleMessage);
          break;
        default:
          console.log(consoleMessage);
      }
    }

    // Write to file in all environments
    this.writeToFile(level, entry);
  }

  // Public logging methods
  info(category: string, message: string, metadata?: Partial<LogEntry>) {
    this.log(LogLevel.INFO, category, message, metadata);
  }

  warn(category: string, message: string, metadata?: Partial<LogEntry>) {
    this.log(LogLevel.WARN, category, message, metadata);
  }

  error(category: string, message: string, metadata?: Partial<LogEntry>) {
    this.log(LogLevel.ERROR, category, message, metadata);
  }

  security(category: string, message: string, metadata?: Partial<LogEntry>) {
    this.log(LogLevel.SECURITY, category, message, metadata);
  }

  // Get recent logs
  getRecentLogs(level: LogLevel, limit: number = 100): LogEntry[] {
    try {
      const fileName = this.getLogFileName(level);
      
      if (!fs.existsSync(fileName)) {
        return [];
      }

      const content = fs.readFileSync(fileName, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.length > 0);
      
      return lines
        .slice(-limit)
        .map(line => this.parseLogLine(line))
        .filter(entry => entry !== null) as LogEntry[];
    } catch (error) {
      console.error('Failed to read log file:', error);
      return [];
    }
  }

  private parseLogLine(line: string): LogEntry | null {
    try {
      // Parse log line format: [timestamp] [level] [category] message | metadata
      const match = line.match(/^\[([^\]]+)\] \[([^\]]+)\] \[([^\]]+)\] (.+)$/);
      
      if (!match) return null;
      
      const [, timestamp, level, category, rest] = match;
      const parts = rest.split(' | ');
      const message = parts[0];
      
      const entry: LogEntry = {
        timestamp,
        level: level as LogLevel,
        category,
        message
      };

      // Parse metadata
      for (let i = 1; i < parts.length; i++) {
        const part = parts[i];
        if (part.startsWith('IP: ')) {
          entry.ip = part.substring(4);
        } else if (part.startsWith('UA: ')) {
          entry.userAgent = part.substring(4);
        } else if (part.startsWith('User: ')) {
          entry.userId = part.substring(6);
        } else if (part.startsWith('Details: ')) {
          try {
            entry.details = JSON.parse(part.substring(9));
          } catch {
            // Ignore invalid JSON
          }
        }
      }

      return entry;
    } catch {
      return null;
    }
  }

  // Get log statistics
  getLogStats(level: LogLevel, hours: number = 24): {
    total: number;
    byCategory: Record<string, number>;
    byHour: Record<string, number>;
  } {
    const logs = this.getRecentLogs(level, 10000);
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const recentLogs = logs.filter(log => 
      new Date(log.timestamp) > cutoffTime
    );

    const byCategory: Record<string, number> = {};
    const byHour: Record<string, number> = {};

    recentLogs.forEach(log => {
      // Count by category
      byCategory[log.category] = (byCategory[log.category] || 0) + 1;
      
      // Count by hour
      const hour = new Date(log.timestamp).toISOString().substring(0, 13);
      byHour[hour] = (byHour[hour] || 0) + 1;
    });

    return {
      total: recentLogs.length,
      byCategory,
      byHour
    };
  }

  // Clear old logs
  clearOldLogs(daysToKeep: number = 30) {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
      const files = fs.readdirSync(this.logDir);
      
      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          console.log(`Deleted old log file: ${file}`);
        }
      });
    } catch (error) {
      console.error('Failed to clear old logs:', error);
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Convenience functions for backward compatibility
export function logSecurityEvent(category: string, message: string, metadata?: Partial<LogEntry>) {
  logger.security(category, message, metadata);
}

export function logError(category: string, message: string, metadata?: Partial<LogEntry>) {
  logger.error(category, message, metadata);
}

export function logWarning(category: string, message: string, metadata?: Partial<LogEntry>) {
  logger.warn(category, message, metadata);
}

export function logInfo(category: string, message: string, metadata?: Partial<LogEntry>) {
  logger.info(category, message, metadata);
}
