# 📊 Guida alla Dashboard di Sicurezza

## 🎯 Panoramica

La nuova **Dashboard di Sicurezza** è un'interfaccia moderna e intuitiva per monitorare gli eventi di sicurezza del tuo sito web in tempo reale. Include grafici interattivi, metriche avanzate e analisi visuale dei dati.

## 🚀 Accesso alla Dashboard

**URL**: `http://localhost:3001/admin/logs` (o il tuo dominio in produzione)

**Requisiti**:
- ✅ Utente autenticato come amministratore
- ✅ Accesso alla sezione protetta `/admin/*`

## 📈 Funzionalità Principali

### 1. **🛡️ Indicatore Livello di Minaccia**

**Posizione**: In alto nella dashboard

**Livelli**:
- 🟢 **LOW** (0-50 eventi/24h): Tutto normale
- 🟡 **MEDIUM** (51-100 eventi/24h): Monitoraggio attivo
- 🔴 **HIGH** (>100 eventi/24h): Attenzione richiesta

**Cosa mostra**:
- Livello di minaccia attuale
- Numero totale di eventi nelle ultime 24 ore
- Messaggio di stato contestuale

### 2. **📊 Metriche Chiave**

**4 Card Principali**:

1. **Eventi Totali (24h)** 📈
   - Numero totale di eventi di sicurezza
   - Trend di monitoraggio

2. **Log Visualizzati** 👁️
   - Numero di log attualmente mostrati
   - Rapporto con il limite impostato

3. **Categorie Attive** 🌐
   - Numero di tipi di eventi diversi
   - Diversità delle minacce

4. **IP Unici** 👥
   - Numero di indirizzi IP sorgente
   - Distribuzione geografica delle richieste

### 3. **📈 Grafici Interattivi**

#### **Attività nelle Ultime 24 Ore** (Area Chart)
- **Cosa mostra**: Distribuzione oraria degli eventi
- **Utilità**: Identificare picchi di attività sospetta
- **Interazione**: Hover per dettagli specifici

#### **Distribuzione per Categoria** (Pie Chart)
- **Cosa mostra**: Percentuale per tipo di evento
- **Utilità**: Capire quali minacce sono più frequenti
- **Colori**: Ogni categoria ha un colore distintivo

#### **Attività Recente (Ultime 12 Ore)** (Line Chart)
- **Cosa mostra**: Trend dettagliato delle ultime ore
- **Utilità**: Monitoraggio in tempo reale
- **Punti**: Ogni ora è rappresentata da un punto

### 4. **🌍 Top IP Addresses**

**Funzionalità**:
- Lista dei 5 IP più attivi
- Numero di eventi per IP
- Indicatori colorati per priorità:
  - 🔴 Primo posto (più attivo)
  - 🟠 Secondo posto
  - 🟡 Terzo posto
  - ⚪ Altri

### 5. **🔧 Controlli Avanzati**

#### **Auto-Refresh**
- **ON**: Aggiornamento automatico ogni 30 secondi
- **OFF**: Aggiornamento manuale
- **Indicatore**: Badge colorato con stato

#### **Filtri**
- **Livello Log**: Sicurezza, Errori, Avvisi, Info
- **Numero Log**: 25, 50, 100, 200 eventi
- **Aggiornamento**: Stato auto-refresh

### 6. **📋 Log Dettagliati**

**Nuove Funzionalità**:
- **Design Moderno**: Card colorate per livello
- **Metadata Strutturata**: IP, User ID, User Agent in card separate
- **Dettagli Tecnici**: JSON formattato con syntax highlighting
- **Numerazione**: Ogni log ha un numero progressivo
- **Timestamp Migliorato**: Data e ora in formato italiano

## 🎨 Design e UX

### **Colori per Livello**
- 🔒 **SECURITY**: Rosso (border-red-200, bg-red-50)
- ❌ **ERROR**: Arancione (border-orange-200, bg-orange-50)
- ⚠️ **WARN**: Giallo (border-yellow-200, bg-yellow-50)
- ℹ️ **INFO**: Blu (border-blue-200, bg-blue-50)

### **Icone Intuitive**
- 🛡️ Shield: Sicurezza
- 📈 TrendingUp: Crescita/Trend
- 👁️ Eye: Visualizzazione
- 🌐 Globe: Rete/IP
- 👥 Users: Utenti
- ⚡ Activity: Attività
- 🕒 Clock: Tempo

### **Responsive Design**
- **Desktop**: Layout a 4 colonne per metriche
- **Tablet**: Layout a 2 colonne
- **Mobile**: Layout a 1 colonna

## 🔍 Come Interpretare i Dati

### **Situazioni Normali**
- Livello minaccia: LOW
- Eventi/24h: < 50
- Categorie: 1-3 tipi
- IP unici: < 10

### **Situazioni da Monitorare**
- Livello minaccia: MEDIUM
- Eventi/24h: 50-100
- Picchi orari improvvisi
- IP singolo con molti eventi

### **Situazioni Critiche**
- Livello minaccia: HIGH
- Eventi/24h: > 100
- Rate limit violations frequenti
- Pattern di attacco coordinato

## 🚨 Azioni Raccomandate

### **Per Livello LOW** 🟢
- ✅ Controllo quotidiano
- ✅ Revisione settimanale dei trend
- ✅ Mantenimento configurazione attuale

### **Per Livello MEDIUM** 🟡
- ⚠️ Controllo ogni 4-6 ore
- ⚠️ Analisi degli IP più attivi
- ⚠️ Verifica pattern temporali

### **Per Livello HIGH** 🔴
- 🚨 Monitoraggio continuo
- 🚨 Blocco IP sospetti
- 🚨 Analisi dettagliata dei log
- 🚨 Possibile escalation al team tecnico

## 🛠️ Personalizzazione

### **Modificare le Soglie**
Nel file `app/admin/logs/page.tsx`, funzione `getThreatLevel()`:

```typescript
const getThreatLevel = () => {
  if (!statistics) return { level: 'LOW', color: 'text-green-600', bgColor: 'bg-green-100' };
  
  const total = statistics.total;
  if (total > 100) return { level: 'HIGH', color: 'text-red-600', bgColor: 'bg-red-100' };
  if (total > 50) return { level: 'MEDIUM', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
  return { level: 'LOW', color: 'text-green-600', bgColor: 'bg-green-100' };
};
```

### **Modificare Colori dei Grafici**
Nel file `app/admin/logs/page.tsx`, costante `CHART_COLORS`:

```typescript
const CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#6366f1', '#22c55e', '#8b5cf6', '#f97316'];
```

### **Modificare Intervallo Auto-Refresh**
Nel file `app/admin/logs/page.tsx`, funzione `toggleAutoRefresh()`:

```typescript
const interval = setInterval(fetchLogs, 30000); // 30 secondi
```

## 📱 Utilizzo Mobile

La dashboard è completamente responsive:

- **Grafici**: Si adattano automaticamente alla larghezza
- **Metriche**: Stack verticale su mobile
- **Log**: Layout ottimizzato per touch
- **Controlli**: Dimensioni appropriate per dita

## 🔗 Integrazione

### **API Endpoint**
- **GET** `/api/admin/logs?level=SECURITY&limit=50&stats=true`
- **DELETE** `/api/admin/logs?days=30`

### **Dati Restituiti**
```json
{
  "logs": [...],
  "statistics": {
    "total": 42,
    "byCategory": {"Security Event": 15, "Rate Limit": 27},
    "byHour": {"2025-06-06T19": 5, "2025-06-06T20": 8}
  },
  "level": "SECURITY",
  "total": 50
}
```

## 🎯 Prossimi Miglioramenti

### **Funzionalità Future**
- 📧 Notifiche email per eventi critici
- 📱 App mobile dedicata
- 🤖 AI per rilevamento anomalie
- 📊 Report automatici PDF
- 🔗 Integrazione Slack/Teams
- 🌍 Mappa geografica degli IP

### **Metriche Aggiuntive**
- Tempo di risposta medio
- Percentuale di successo/fallimento
- Analisi user agent
- Correlazione eventi temporali

---

**La tua dashboard di sicurezza è ora un centro di controllo professionale per monitorare la sicurezza del tuo sito web!** 🚀🔒
