import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowR<PERSON>, Bug, Users, AlertTriangle, Shield, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Cos\'è la Scabbia? | Guida Completa - ScabbiaSintomi.it',
  description: 'Scopri cos\'è la scabbia, come si manifesta e quali sono le sue caratteristiche principali. Guida medica completa con informazioni affidabili.',
  keywords: 'scabbia, acari, Sarcoptes scabiei, infestazione cutanea, prurito, sintomi',
};

const riskFactors = [
  {
    icon: Users,
    title: 'Ambienti affollati',
    description: 'Case di cura, dormitori, carceri'
  },
  {
    icon: Clock,
    title: 'Et<PERSON> avanzata',
    description: 'Anziani in strutture di assistenza'
  },
  {
    icon: Shield,
    title: 'Sistema immunitario',
    description: 'Persone immunocompromesse'
  },
  {
    icon: Users,
    title: 'Contatti frequenti',
    description: 'Operatori sanitari, caregivers'
  }
];

const keyFacts = [
  'Non è legata alla scarsa igiene personale',
  'Chiunque può essere colpito dalla scabbia',
  'È altamente contagiosa tramite contatto diretto',
  'Richiede trattamento medico specifico'
];

export default function CosELaScabbia() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Informazioni Mediche
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                Cos&apos;è la <span className="text-gradient">Scabbia</span>?
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Una guida completa per comprendere questa comune infestazione cutanea,
                le sue cause e come riconoscerla.
              </p>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">

              {/* Definition Card */}
              <Card className="border-0 shadow-large bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center">
                      <Bug className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-2xl">Definizione</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    La scabbia è un&apos;<strong>infestazione cutanea contagiosa</strong> causata da un acaro microscopico
                    chiamato <em>Sarcoptes scabiei</em>. Questi acari si insediano nello strato superiore della pelle,
                    dove depongono le uova, causando intenso prurito e un&apos;eruzione cutanea che spesso peggiora di notte.
                  </p>

                  <div className="bg-primary/5 p-4 rounded-lg border border-primary/10">
                    <p className="text-sm text-primary font-medium">
                      💡 <strong>Fatto importante:</strong> La scabbia colpisce milioni di persone in tutto il mondo
                      ogni anno, indipendentemente dall&apos;età, sesso o condizione socioeconomica.
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* How it manifests */}
              <Card className="border-0 shadow-large bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-3">
                    <Clock className="w-6 h-6 text-primary" />
                    <span>Come si manifesta</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed">
                    I sintomi della scabbia compaiono tipicamente da <strong>2 a 6 settimane</strong> dopo l&apos;infezione iniziale.
                    Nelle persone che hanno già avuto la scabbia, i sintomi possono manifestarsi entro pochi giorni.
                  </p>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <h4 className="font-semibold text-foreground">Sintomo principale:</h4>
                      <p className="text-sm text-muted-foreground">
                        Intenso prurito, particolarmente fastidioso di notte o dopo un bagno caldo
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-semibold text-foreground">Manifestazioni cutanee:</h4>
                      <p className="text-sm text-muted-foreground">
                        Piccoli rilievi, vescicole e caratteristiche &quot;gallerie&quot; sottocutanee
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Transmission */}
              <Card className="border-0 shadow-large bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-3">
                    <Users className="w-6 h-6 text-primary" />
                    <span>Come si trasmette</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed">
                    La scabbia si diffonde principalmente attraverso il <strong>contatto pelle a pelle prolungato</strong>
                    con una persona infetta. La trasmissione può avvenire anche attraverso la condivisione di
                    biancheria da letto, abbigliamento o mobili infestati.
                  </p>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="p-4 bg-destructive/5 rounded-lg border border-destructive/10">
                      <h4 className="font-semibold text-destructive mb-2">Trasmissione diretta</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Contatto pelle a pelle prolungato</li>
                        <li>• Rapporti intimi</li>
                        <li>• Contatto familiare stretto</li>
                      </ul>
                    </div>
                    <div className="p-4 bg-warning/5 rounded-lg border border-warning/10">
                      <h4 className="font-semibold text-warning mb-2">Trasmissione indiretta</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Condivisione di biancheria</li>
                        <li>• Abbigliamento infestato</li>
                        <li>• Mobili imbottiti</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Risk Factors */}
              <Card className="border-0 shadow-large bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-3">
                    <AlertTriangle className="w-6 h-6 text-primary" />
                    <span>Fattori di rischio</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 sm:grid-cols-2">
                    {riskFactors.map((factor, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <factor.icon className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-foreground">{factor.title}</h4>
                          <p className="text-sm text-muted-foreground">{factor.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Important Facts */}
              <Card className="border-0 shadow-large bg-gradient-to-br from-success/5 to-success/10 border-success/20">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-3 text-success">
                    <CheckCircle className="w-6 h-6" />
                    <span>Importante da sapere</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-3">
                    {keyFacts.map((fact, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <CheckCircle className="w-4 h-4 text-success flex-shrink-0" />
                        <span className="text-foreground">{fact}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-success/10 rounded-lg border border-success/20">
                    <p className="text-success font-medium">
                      Se sospetti di avere la scabbia, è importante consultare un medico per una
                      diagnosi accurata e un trattamento adeguato. Non tentare l&apos;autodiagnosi o l&apos;automedicazione.
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Hai dei sintomi sospetti?
                </h3>
                <p className="text-muted-foreground">
                  Fai il nostro test AI avanzato per una valutazione immediata dei tuoi sintomi.
                </p>
                <div className="bg-gradient-to-r from-primary/10 to-success/10 rounded-lg p-4 mb-6 border border-primary/20">
                  <div className="flex items-center justify-center space-x-2 text-sm">
                    <span className="font-semibold text-primary">Solo €1</span>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-muted-foreground">Analisi simile a visita dermatologica (€100+)</span>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-muted-foreground">Non sostituisce diagnosi medica</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/scabbia-checker">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Test AI €1
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/sintomi">
                    <Button variant="outline" size="lg">
                      Scopri i Sintomi
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
