import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from 'next-themes'
import { Toaster } from 'react-hot-toast'
import { CookieBanner } from '@/components/cookie-banner'
import './globals.css'

export const metadata: Metadata = {
  title: 'ScabbiaSintomi.it – Test AI Scabbia €1 vs €100+ Dermatologo',
  description:
    'Test AI avanzato per scabbia a soli €1. Analisi accurata dei sintomi comparabile a visita dermatologica (€100+). Non sostituisce diagnosi medica professionale.',
  keywords: 'scabbia, sintomi, test AI, diagnosi, €1, dermatologo, trattamento, prevenzione, acari, prurito',
  authors: [{ name: 'ScabbiaSintomi.it' }],
  creator: 'ScabbiaSintomi.it',
  publisher: 'ScabbiaSintomi.it',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://www.scabbiasintomi.it'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'ScabbiaSintomi.it – Test AI Scabbia €1 vs €100+ Dermatologo',
    description: 'Test AI avanzato per scabbia a soli €1. Analisi accurata comparabile a visita dermatologica ma non sostitutiva.',
    url: 'https://www.scabbiasintomi.it',
    siteName: 'ScabbiaSintomi.it',
    locale: 'it_IT',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ScabbiaSintomi.it – Test AI Scabbia €1 vs €100+ Dermatologo',
    description: 'Test AI avanzato per scabbia a soli €1. Analisi accurata comparabile a visita dermatologica.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
})

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="it" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-background text-foreground`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">
              {children}
            </div>
            <CookieBanner />
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--card))',
                color: 'hsl(var(--card-foreground))',
                border: '1px solid hsl(var(--border))',
                borderRadius: 'var(--radius)',
              },
              success: {
                iconTheme: {
                  primary: 'hsl(var(--success))',
                  secondary: 'hsl(var(--success-foreground))',
                },
              },
              error: {
                iconTheme: {
                  primary: 'hsl(var(--destructive))',
                  secondary: 'hsl(var(--destructive-foreground))',
                },
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  )
}

