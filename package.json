{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "security:audit": "node scripts/security-audit.js", "security:test": "npm test -- __tests__/security.test.ts", "logs:test": "node scripts/test-logging.js"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/bcryptjs": "^2.4.6", "@types/validator": "^13.15.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "helmet": "^8.1.0", "lucide-react": "^0.511.0", "marked": "^15.0.12", "next": "latest", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.1.1", "rate-limiter-flexible": "^7.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "validator": "^13.15.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}